<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Widget Integration</title>
    <style>
      #outputDiv {
        margin: 10px;
        padding: 20px;
        border: 1px solid black;
        height: 178px;
        overflow: auto;
      }
    </style>
  </head>
  <body>
    <div class="widget-container"></div>
    <!-- Define custom styles -->
    <script>
      window.customStyles = {
        message: {
          sent: {
            background: "#FE863D",
            borderRadius: "24px",
            color: "#fff",
            fontSize: "12px",
          },
          received: {
            borderRadius: "24px",
          },
        },
        button: {
          background: "#FE863D",
          disabled: {
            background: "#FE863D",
            opacity: "0.4",
          },
        },
        messageHeader: {
          button: {
            hover: {
              background: "#FFA64D", // Desired hover background color
            },
          },
        },
      };
    </script>
    <!-- Load your widget bundle -->
    <script>
      document.addEventListener("DOMContentLoaded", async () => {
        try {
          const loadScript = (src) =>
            new Promise((resolve) => {
              const script = document.createElement("script");
              script.onload = resolve;
              script.src = src;
              document.body.appendChild(script);
            });

          await loadScript("http://127.0.0.1:8080/page.bundle.js");

          if (typeof widget !== "undefined") {
            widget.init({
              organizationId: "6672baa6ccf533e516f4a3c5",
              agentId: "664326cfa20ccf48d784fb70",
              styles: window.customStyles,
              modal: {
                modalHeaderText: "AI Modal",
                handleAiModal: () => {},
                isModalOpen: false,
              },
              widgetCallBack: (err, text) => {
                if (text)
                  document.getElementById(
                    "outputDiv"
                  ).textContent = `Text copied to clipboard: ${text}`;
              },
            });
            widget.sendMessageExternal("Hello, from widget");
          } else {
            console.error("Widget is not defined.");
          }
        } catch (error) {
          console.error("Error:", error);
        }
      });
    </script>
  </body>
</html>
