# daiquiri
# 🧩 AI Widget Integration Guide

This guide helps you integrate the AI widget into any project — whether you're using plain HTML or frameworks like Next.js.

---

## 📥 Embed in Static HTML Page

Use this snippet inside your HTML page:

> ✅ Replace `YOUR_ORG_ID` and `YOUR_AGENT_ID` with real values.

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Widget Integration</title>
  </head>
  <body>
    <div class="widget-container" id="widget-container"></div>

    <!-- React & ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Widget Bundle -->
    <script src="https://im-files-prod-20200924143011010000000001.s3.eu-west-2.amazonaws.com/widget/page.bundle.js"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        function tryInitWidget() {
          if (window.widget && typeof window.widget.init === 'function') {
            window.widget.init({
              organizationId: 'YOUR_ORG_ID',
              agentId: 'YOUR_AGENT_ID',
              widgetCallBack: function (err, data) {
                console.log('Widget callback:', err, data);
              },
              discardCallBack: function () {
                console.log('Output discarded');
              },
              discardButtonText: 'Discard',
              callbackButtonText: 'Send',
              styles: {
                message: {
                  sent: {
                    background: '#FE863D',
                    borderRadius: '24px',
                    color: '#fff',
                    fontSize: '12px',
                  },
                  received: {
                    background: '#FE863D',
                    borderRadius: '24px',
                    color: '#fff',
                    fontSize: '12px',
                  },
                },
                button: {
                  background: '#FE863D',
                  disabled: {
                    background: '#FE863D',
                    opacity: '0.4',
                  },
                },
              },
            });
          } else {
            setTimeout(tryInitWidget, 100);
          }
        }

        tryInitWidget();
      });
    </script>
  </body>
</html>
```

---

## ⚙️ Integrate in Next.js

Create a component like `InitialiseWidget.js`:

```jsx
'use client';
import { useEffect } from 'react';

function addPlaceholderStyles() {
  if (typeof document !== 'undefined') {
    const styleSheet = document.styleSheets[0];
    styleSheet.insertRule(
      `#message-input::placeholder {
        font-family: MaisonNeue;
        color: #858585;
      }`,
      styleSheet.cssRules.length
    );
  }
}

const InitialiseWidget = ({ handleAiModal, callbackFunction, message }) => {
  useEffect(() => {
    if (typeof widget !== 'undefined') {
      widget.init({
        organizationId: process.env.organizationId,
        agentId: process.env.agentId,
        widgetCallBack: callbackFunction,
        discardCallBack: handleAiModal,
        discardButtonText: 'Discard',
        callbackButtonText: 'Use It',
        styles: {
          message: {
            sent: {
              background: '#FE863D',
              borderRadius: '24px',
              color: '#fff',
              fontSize: '12px',
            },
            received: {
              background: '#FE863D',
              borderRadius: '24px',
              color: '#fff',
              fontSize: '12px',
            },
          },
          button: {
            background: '#FE863D',
            disabled: {
              background: '#FE863D',
              opacity: '0.4',
            },
          },
        },
      });

      if (message) {
        widget.sendMessageExternal(message);
      }

      addPlaceholderStyles();
    } else {
      console.warn('Widget not found on window object');
    }
  }, []);

  return <div className="widget-container"></div>;
};

export default InitialiseWidget;
```

---

## 🎨 Custom Styling

Customize the widget appearance via `styles` in `init()`:

```js
styles: {
  message: {
    sent: {
      background: '#FE863D',
      borderRadius: '24px',
      color: '#fff',
      fontSize: '12px',
    },
    received: {
      background: '#FE863D',
      borderRadius: '24px',
      color: '#fff',
      fontSize: '12px',
    },
  },
  button: {
    background: '#FE863D',
    disabled: {
      background: '#FE863D',
      opacity: '0.4',
    },
  },
}
```

To change the placeholder styling:

```js
const styleSheet = document.styleSheets[0];
styleSheet.insertRule(
  `#message-input::placeholder {
    font-family: MaisonNeue;
    color: #858585;
  }`,
  styleSheet.cssRules.length
);
```

---

## 📤 Send Message Programmatically

Send a message into the widget using:

```js
widget.sendMessageExternal('Hello from outside!');
```

This helps in programmatically triggering a conversation.

---

## 📚 Widget API Reference

### `widget.init(options)`

| Option               | Type     | Description                                 |
|----------------------|----------|---------------------------------------------|
| `organizationId`     | string   | Your Org ID                                 |
| `agentId`            | string   | AI Agent ID                                 |
| `widgetCallBack`     | function | Called when user clicks "Send"              |
| `discardCallBack`    | function | Called when user clicks "Discard"           |
| `discardButtonText`  | string   | Custom discard button label                 |
| `callbackButtonText` | string   | Custom send button label                    |
| `styles`             | object   | Appearance customization object             |

---

## 📬 `widget.sendMessageExternal(message)`

Inject a message into the widget.

**Example:**

```js
widget.sendMessageExternal('What is the weather today?');
```

---

## 🧪 Testing the Integration

After adding the widget:

1. Open your site.
2. Ensure scripts load properly (React + Widget).
3. Check browser console for init success.
4. Interact with the widget: send & discard.
5. Confirm callbacks trigger as expected.

---

## ✅ Summary

| Feature                      | Available |
|-----------------------------|-----------|
| Static HTML Integration     | ✅        |
| Next.js Integration         | ✅        |
| Custom Styling              | ✅        |
| External Message Injection  | ✅        |
| Callback Handling           | ✅        |

---

## 📝 License & Usage

This widget is provided as part of your organization’s platform. Do not redistribute without permission. For usage terms, contact your admin.
