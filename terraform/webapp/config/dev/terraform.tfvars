aws_region  = "eu-west-2"
aws_profile = "533267084389"
env         = "dev"

public_fqdn = "app.dev.daiquiri.activate.bar"

service_count              = "1"
app_name                   = "daiquiri"
app_component              = "webapp"
service_port               = "3000"
service_memory_reservation = "128"
service_memory             = "256"
service_cpu                = "128"
ulimit_nofile              = "1024"

# ENV vars
env_vars = {
  API_BASE_URL = {
    type  = "String"
    value = "https://api.dev.daiquiri.activate.bar"
  }
  SPRITZ_API_URL = {
    type  = "String"
    value = "https://api.dev.spritz.cafe"
  }
  SPRITZ_API_TOKEN = {
    type  = "String"
    value = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbnw2NGMyNzE2NGJhMWEzNGUzZjBkZWE1OWEiLCJpYXQiOjE3MjEyOTAzMzMsImV4cCI6MTg3ODk3MDMzM30.VeANMoE3WVMf0OmqXLtgIwub-GfyOxQ8XC9i6oUs2g4"
  }
  AGENT_ID = {
    type  = "String"
    value = "664326cfa20ccf48d784fb70"
  }
  ORGANIZATION_ID = {
    type  = "String"
    value = "6672baa6ccf533e516f4a3c5"
  }
  CALL_BACK_URL = {
    type  = "String"
    value = "https://api.dev.daiquiri.activate.bar/v1/chat/agent"
  }
  WIDGET = {
    type  = "String"
    value = "no"
  }
}
