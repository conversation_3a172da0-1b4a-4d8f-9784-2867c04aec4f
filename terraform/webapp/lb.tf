resource "aws_lb_target_group" "this" {
  name     = "${var.env}-${local.service_name}-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = data.terraform_remote_state.vpc.outputs.vpc_id
  # target_type = "ip"

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200"
    timeout             = "3"
    path                = "/"
    unhealthy_threshold = "2"
  }

  lifecycle {
    create_before_destroy = false
  }
}

resource "aws_lb_listener_rule" "static" {
  listener_arn = data.terraform_remote_state.alb.outputs.https_lb_listener_arn

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.this.id
  }

  condition {
    host_header {
      values = [var.public_fqdn]
    }
  }
}
