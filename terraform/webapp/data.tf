data "terraform_remote_state" "vpc" {
  backend = "s3"

  config = {
    region  = "eu-west-2"
    bucket  = "${var.aws_profile}-tf-state"
    key     = "aws/_global/vpc/terraform.tfstate"
    profile = var.aws_profile
  }
}

data "terraform_remote_state" "route53" {
  backend = "s3"

  config = {
    region  = "eu-west-2"
    bucket  = "${var.aws_profile}-tf-state"
    key     = "aws/_global/route53/terraform.tfstate"
    profile = var.aws_profile
  }
}

data "terraform_remote_state" "ecs" {
  backend = "s3"

  config = {
    region  = "eu-west-2"
    bucket  = "${var.aws_profile}-tf-state"
    key     = "aws/${var.env}/ecs/terraform.tfstate"
    profile = var.aws_profile
  }
}

data "terraform_remote_state" "alb" {
  backend = "s3"

  config = {
    region  = "eu-west-2"
    bucket  = "${var.aws_profile}-tf-state"
    key     = "aws/_global/alb/terraform.tfstate"
    profile = var.aws_profile
  }
}

data "terraform_remote_state" "s3" {
  backend = "s3"

  config = {
    region  = "eu-west-2"
    bucket  = "${var.aws_profile}-tf-state"
    key     = "aws/${var.env}/s3/terraform.tfstate"
    profile = var.aws_profile
  }
}
