# ECS task execution role data
data "aws_iam_policy_document" "ecs_task_execution_assume_role" {
  statement {
    sid    = "AllowECSTasksToAssumeRole"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

# ECS task execution role
resource "aws_iam_role" "ecs_task_execution" {
  name               = "${var.env}-${local.service_name}-${var.aws_region}-task-execution"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_execution_assume_role.json
}

# ECS task execution role policy attachment
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role" {
  role       = aws_iam_role.ecs_task_execution.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
  depends_on = [aws_iam_role.ecs_task_execution]
}

# ECS task execution role data
data "aws_iam_policy_document" "ecs_task_assume_role" {
  statement {
    sid    = "AllowECSTasksToAssumeRole"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

// task role policy document
data "aws_iam_policy_document" "ecs_task_role" {
  statement {
    sid     = "AllowServiceToAccessSecretsFromSSM"
    effect  = "Allow"
    actions = ["ssm:GetParametersByPath"]

    resources = [
      "arn:aws:ssm:${var.aws_region}:${var.aws_profile}:parameter/${var.env}/${var.app_name}/${var.app_component}/*",
    ]
  }

  statement {
    sid       = "AllowAccessToKMSForDecryptingSSMParameters"
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = ["arn:aws:kms:${var.aws_region}:${var.aws_profile}:alias/aws/ssm"]
  }

  statement {
    sid = "AllowReadingResourcesForTags"

    effect = "Allow"

    actions = ["tag:GetResources"]

    resources = ["*"]
  }

  statement {
    sid    = "AllowAccessToS3FilesAdapterBucket"
    effect = "Allow"

    actions = ["s3:*"]

    resources = [
      data.terraform_remote_state.s3.outputs.im_files_bucket_arn,
      "${data.terraform_remote_state.s3.outputs.im_files_bucket_arn}/*"
    ]
  }
}

// task role
resource "aws_iam_role" "ecs_task" {
  name               = "${var.env}-${local.service_name}-${var.aws_region}-task"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_assume_role.json
}

// task role policy
resource "aws_iam_policy" "ecs_task" {
  name   = "${var.env}-${local.service_name}-${var.aws_region}-ecs-task"
  policy = data.aws_iam_policy_document.ecs_task_role.json
}

# ECS task execution role policy attachment
resource "aws_iam_role_policy_attachment" "ecs_task_role" {
  role       = aws_iam_role.ecs_task.name
  policy_arn = aws_iam_policy.ecs_task.arn
  depends_on = [aws_iam_role.ecs_task, aws_iam_policy.ecs_task]
}
