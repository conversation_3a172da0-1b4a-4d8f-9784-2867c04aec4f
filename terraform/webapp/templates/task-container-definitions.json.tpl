[
  {
    "name": "${service_name}",
    "image": "${service_image}",
    "cpu": ${service_cpu},
    "memory": ${service_memory},
    "memoryReservation": ${service_memory_reservation},
    "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "${awslogs_group}",
          "awslogs-region": "${aws_region}",
          "awslogs-datetime-format": "${awslogs_datetime_format}"
        }
    },
    "portMappings": [
      {
        "hostPort": 0,
        "containerPort": ${service_port},
        "protocol": "tcp"
      }
    ],
    "ulimits": [
      {
        "name": "nofile",
        "softlimit": ${ulimit_nofile},
        "hardlimit": ${ulimit_nofile}
      }
    ]
  }
]
