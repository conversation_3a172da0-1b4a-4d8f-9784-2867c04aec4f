variable "aws_profile" {
  description = "The AWS profile"
  type        = string
  default     = ""
}

variable "aws_region" {
  description = "The AWS region"
  type        = string
  default     = "eu-west-2"
}

variable "env" {
  description = "A logical name that will be used as prefix and tag for the created resources."
  type        = string
  default     = "dev"
}

variable "app_name" {
  description = "Application Name"
  type        = string
}

variable "app_component" {
  description = "Application Component"
  type        = string
}

variable "image_tag" {
  description = "Docker image tagto run in the ECS cluster"
  type        = string
  default     = "latest"
}

variable "service_port" {
  description = "Port exposed by the docker image to redirect traffic to"
}

variable "service_count" {
  description = "Number of docker containers to run"
  default     = 1
}

variable "health_check_path" {
  description = "Load Balancer health check path"
  type        = string
  default     = "/"
}

variable "service_cpu" {
  description = "Instance CPU units to provision"
  type        = string
  default     = "128"
}

variable "service_memory_reservation" {
  description = "Service memory soft limit (in MiB)"
  type        = string
  default     = "128"
}

variable "service_memory" {
  description = "Service memory hard limit (in MiB)"
  type        = string
  default     = "256"
}

variable "ulimit_nofile" {
  description = "Sets the Linux ulimit on number of file descriptors both hard and soft limits, defaults to 1024."
  default     = "2048"
}

variable "public_fqdn" {
  description = "Servie FQDN on public interface"
}

# ENV vars
variable "env_vars" {
  description = "A nested map of ENV vars"
  type = map(object({
    type  = string
    value = string
  }))
}
