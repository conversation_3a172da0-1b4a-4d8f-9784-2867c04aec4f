# Secrets with KMS: uncomment if needed
#data "aws_kms_secrets" "secret_env_vars" {
#  dynamic "secret" {
#    for_each = [for k, s in var.env_vars : {
#      name    = k
#      payload = s.value
#      } if s.type == "SecureString"
#    ]
#
#    content {
#      name    = secret.value["name"]
#      payload = secret.value["payload"]
#    }
#  }
#}

resource "aws_ssm_parameter" "env_vars" {
  for_each = var.env_vars

  name  = "/${var.env}/${var.app_name}/${var.app_component}/${upper(each.key)}"
  type  = each.value["type"]
  value = each.value["value"]
  #value = each.value["type"] != "SecureString" ? each.value["value"] : data.aws_kms_secrets.secret_env_vars.plaintext[each.key]
}
