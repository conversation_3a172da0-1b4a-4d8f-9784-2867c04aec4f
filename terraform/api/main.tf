terraform {
  backend "s3" {
  }
}

provider "aws" {
  profile = var.aws_profile
  region  = var.aws_region

  default_tags {
    tags = local.default_tags
  }
}

resource "aws_cloudwatch_log_group" "this" {
  name              = "/ecs/${var.env}/${var.app_name}/${var.app_component}"
  retention_in_days = 90
}

data "aws_ecr_image" "this" {
  repository_name = "${var.env}/${var.app_name}/${var.app_component}"
  image_tag       = var.image_tag
}

data "template_file" "container_definitions" {
  template = file("./templates/task-container-definitions.json.tpl")

  vars = {
    service_name  = local.service_name
    service_image = "${data.terraform_remote_state.ecs.outputs.aws_ecr_repository[var.app_name][var.app_component]["repository_url"]}:${var.image_tag}@${data.aws_ecr_image.this.image_digest}"
    service_port  = var.service_port

    service_cpu                = var.service_cpu
    service_memory_reservation = var.service_memory_reservation
    service_memory             = var.service_memory
    ulimit_nofile              = var.ulimit_nofile

    aws_region              = var.aws_region
    awslogs_group           = aws_cloudwatch_log_group.this.name
    awslogs_datetime_format = "%Y-%m-%dT%H:%M:%S%L"
  }
}

resource "aws_ecs_task_definition" "this" {
  family                = "${var.env}-${local.service_name}"
  execution_role_arn    = aws_iam_role.ecs_task_execution.arn
  task_role_arn         = aws_iam_role.ecs_task.arn
  network_mode          = "bridge"
  container_definitions = data.template_file.container_definitions.rendered

  depends_on = [
    aws_ssm_parameter.env_vars
  ]
}

resource "aws_ecs_service" "this" {
  name            = local.service_name
  cluster         = data.terraform_remote_state.ecs.outputs.cluster_id
  task_definition = aws_ecs_task_definition.this.arn
  desired_count   = var.service_count

  # network_configuration {
  #   security_groups = [data.terraform_remote_state.ecs.outputs.ecs_tasks_security_group_id]
  #   subnets = [
  #     data.terraform_remote_state.vpc.outputs.default_subnet_az1_id,
  #     data.terraform_remote_state.vpc.outputs.default_subnet_az2_id,
  #     data.terraform_remote_state.vpc.outputs.default_subnet_az3_id
  #   ]
  #   assign_public_ip = true
  # }

  ordered_placement_strategy {
    field = "instanceId"
    type  = "spread"
  }

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.this.id
    container_name   = local.service_name
    container_port   = var.service_port
  }

  depends_on = [
    aws_lb_listener_rule.static,
    aws_iam_role_policy_attachment.ecs_task_execution_role,
    aws_iam_role_policy_attachment.ecs_task_role
  ]
}
