version: 2.1

orbs:
  swissknife: roopakv/swissknife@0.65.0
  aws-ecr: circleci/aws-ecr@7.2.0
  slack: circleci/slack@4.13.1

# Jobs definitions
jobs:

  build-image:
    docker:
      - image: cimg/node:20.10.0-browsers
    working_directory: ~/project
    parameters:
      dir:
        type: string
        description: The dir of package to run build for.
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
          version: default
      - run:
          name: Prepare ENV Vars according to branch
          command: |
            echo "I am in ${CIRCLE_BRANCH} branch!"
            SET_ENV=dev
            if [ "${CIRCLE_BRANCH}" == "staging" ]; then
              SET_ENV=stage
            fi
            if [ "${CIRCLE_BRANCH}" == "production" ]; then
              SET_ENV=prod
            fi
            echo "SET_ENV: ${SET_ENV}"
            echo "export ENV=${SET_ENV}" >> $BASH_ENV
            GIT_SHORT_SHA1=$(git rev-parse --short HEAD)
            echo "export IMAGE_TAG=${GIT_SHORT_SHA1}" >> $BASH_ENV
      - run:
          name: Set webapp NextJS env file
          working_directory: ~/project/packages/<< parameters.dir >>
          command: |
            if [ << parameters.dir >> == "webapp" ]; then
              cp ./.env.${ENV} ./.env.local
            fi
      - aws-ecr/build-and-push-image:
          account-url: AWS_ECR_ACCOUNT_URL
          aws-access-key-id: AWS_ACCESS_KEY_ID
          aws-secret-access-key: AWS_SECRET_ACCESS_KEY
          dockerfile: Dockerfile
          no-output-timeout: 20m
          path: ./packages/<< parameters.dir >>
          region: AWS_REGION
          repo: ${ENV}/${CIRCLE_PROJECT_REPONAME}/<< parameters.dir >>
          extra-build-args: '--build-arg ENV=${ENV}'
          tag: 'latest,${IMAGE_TAG}'
      - slack/notify:
          branch_pattern: develop,production,staging
          channel: C075KBU9ZK5
          event: fail
          template: basic_fail_1
      - slack/notify:
          branch_pattern: develop,production,staging
          channel: C075KBU9ZK5
          event: pass
          template: basic_success_1

  run-terraform:
    docker:
      - image: cimg/node:20.10.0
    working_directory: ~/project
    parameters:
      dir:
        type: string
        description: The dir of package to run terraform for.
    steps:
      - checkout
      - run:
          name: Prepare AWS credentials
          command: |
            mkdir -p ~/.aws
            echo "[${AWS_PROFILE}]" >> ~/.aws/credentials
            echo "aws_access_key_id = ${AWS_ACCESS_KEY_ID}" >> ~/.aws/credentials
            echo "aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY}" >> ~/.aws/credentials    
      - checkout
      - run:
          name: Install terraform
          working_directory: ~/project/
          command: |
            mkdir -p /home/<USER>/.local/bin
            wget -q https://releases.hashicorp.com/terraform/<< pipeline.parameters.tf_version >>/terraform_<< pipeline.parameters.tf_version >>_linux_amd64.zip -O /tmp/terraform.zip
            unzip -d /home/<USER>/.local/bin/ /tmp/terraform.zip
      - run:
          name: Prepare ENV Vars according to branch
          working_directory: ~/project/terraform/<< parameters.dir >>
          command: |
            echo "I am in ${CIRCLE_BRANCH} branch!"
            SET_ENV=dev
            if [ "${CIRCLE_BRANCH}" == "staging" ]; then
              SET_ENV=stage
            fi
            if [ "${CIRCLE_BRANCH}" == "production" ]; then
              SET_ENV=prod
            fi
            echo "SET_ENV: ${SET_ENV}"
            echo "export ENV=${SET_ENV}" >> $BASH_ENV
      - run:
          name: Run terraform init
          working_directory: ~/project/terraform/<< parameters.dir >>
          command: |
            echo "Running terraform init for ${ENV} environment"
            echo "CWD: $(pwd)"
            terraform init -no-color -backend-config=./config/${ENV}/backend.hcl
      - run:
          name: Run terraform plan
          working_directory: ~/project/terraform/<< parameters.dir >>
          command: |
            echo "Running terraform plan for ${ENV} environment"
            echo "CWD: $(pwd)"
            terraform plan -no-color -var-file=./config/${ENV}/terraform.tfvars -out ${ENV}.plan
      - run:
          name: Run terraform apply
          working_directory: ~/project/terraform/<< parameters.dir >>
          command: |
            echo "Running terraform aplly for ${ENV} environment"
            echo "CWD: $(pwd)"
            terraform apply -no-color ${ENV}.plan
      - slack/notify:
          branch_pattern: develop,production,staging
          channel: C075KBU9ZK5
          event: fail
          template: basic_fail_1
      - slack/notify:
          branch_pattern: develop,production,staging
          channel: C075KBU9ZK5
          event: pass
          template: basic_success_1

  trigger-needed-workflows:
    docker:
      - image: cimg/node:20.10.0
    working_directory: /home/<USER>/project/
    steps:
      - checkout
      - run:
          name: Set SWISSKNIFE_BASE_BRANCH according to current branch
          command: |
            echo "I am in ${CIRCLE_BRANCH} branch!"
            SET_SWISSKNIFE_BASE_BRANCH=develop
            if [ "${CIRCLE_BRANCH}" == "staging" ]; then
              SET_SWISSKNIFE_BASE_BRANCH=staging
            fi
            if [ "${CIRCLE_BRANCH}" == "production" ]; then
              SET_SWISSKNIFE_BASE_BRANCH=production
            fi
            echo "SET_SWISSKNIFE_BASE_BRANCH: ${SET_SWISSKNIFE_BASE_BRANCH}"
            echo "export SWISSKNIFE_BASE_BRANCH=${SET_SWISSKNIFE_BASE_BRANCH}" >> $BASH_ENV
      - swissknife/trigger-workflows-for-all-modified:
          base-branch: ${SWISSKNIFE_BASE_BRANCH}
          run-mode-for-base-branch: run_for_last_commit
          use-divergence-point: true
          code-param-name-map: '
            [
            {"regex": "^(packages\/api).*", "param_name": "run_api_workflow"},
            {"regex": "^(packages\/webapp).*", "param_name": "run_webapp_workflow"}
            ]'
          additional-param-map: '{"run_trigger_workflow": false}'
          use-swissknife-sha: true

# Workflows definitions
workflows:
  api:
    when: << pipeline.parameters.run_api_workflow >>
    jobs:

      # Only develop section
      - build-image:
          context:
            - global
            - activate-intelligence
          dir: api
          name: build-api
          filters:
            branches:
              only:
                - develop
      - run-terraform:
          context:
            - global
            - activate-intelligence
          dir: api
          name: deploy-api
          requires:
            - build-api
          filters:
            branches:
              only:
                - develop

      # Only production section
      - build-image:
          context:
            - global
            - activate-intelligence
          dir: api
          name: build-api-prod
          filters:
            branches:
              only:
                - production
      - run-terraform:
          context:
            - global
            - activate-intelligence
          dir: api
          name: deploy-api-prod
          requires:
            - build-api-prod
          filters:
            branches:
              only:
                - production

  webapp:
    when: << pipeline.parameters.run_webapp_workflow >>
    jobs:
      - build-image:
          context:
            - global
            - activate-intelligence
          dir: webapp
          name: build-webapp
          filters:
            branches:
              only:
                - develop
      - run-terraform:
          context:
            - global
            - activate-intelligence
          dir: webapp
          name: deploy-webapp
          requires:
            - build-webapp
          filters:
            branches:
              only:
                - develop
      # Only staging section
      - build-image:
          context:
            - global
            - activate-intelligence
          dir: webapp
          name: build-webapp-stage
          filters:
            branches:
              only:
                - staging
      - run-terraform:
          context:
            - global
            - activate-intelligence
          dir: webapp
          name: deploy-webapp-stage
          requires:
            - build-webapp-stage
          filters:
            branches:
              only:
                - staging
      # Only production section
      - build-image:
          context:
            - global
          dir: webapp
          name: build-webapp-prod
          filters:
            branches:
              only:
                - production
      - run-terraform:
          context:
            - global
            - activate-intelligence
          dir: webapp
          name: deploy-webapp-prod
          requires:
            - build-webapp-prod
          filters:
            branches:
              only:
                - production

  trigger-needed-workflows:
    when: << pipeline.parameters.run_trigger_workflow >>
    jobs:
      - trigger-needed-workflows

parameters:
  run_api_workflow:
    default: false
    type: boolean
  run_webapp_workflow:
    default: false
    type: boolean
  run_trigger_workflow:
    default: true
    type: boolean
  swissknife_sha:
    default: ''
    type: string
  tf_version:
    default: '1.7.3'
    type: string
