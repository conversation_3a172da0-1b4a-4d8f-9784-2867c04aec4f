/**
 * Debug <PERSON><PERSON><PERSON> for Page Reload Connection Issues
 * 
 * Run this script in the browser console after page reload to diagnose connection issues.
 * 
 * Usage:
 * 1. Open browser console (F12)
 * 2. Copy and paste this entire script
 * 3. Press Enter to run
 * 4. Check the output for issues
 */

(function() {
  console.log('=== DAIQUIRI CONNECTION DIAGNOSTIC ===');
  console.log('Timestamp:', new Date().toISOString());
  
  // Check URL parameters
  console.log('\n1. URL PARAMETERS:');
  const urlParams = new URLSearchParams(window.location.search);
  const chatId = urlParams.get('id');
  const agentId = urlParams.get('agentID');
  
  console.log('Current URL:', window.location.href);
  console.log('Chat ID (id):', chatId);
  console.log('Agent ID (agentID):', agentId);
  
  if (!chatId && !agentId) {
    console.warn('⚠️ No URL parameters found - this will cause history loss');
  } else if (chatId && !agentId) {
    console.warn('⚠️ Chat ID found but no Agent ID - agent selection may be lost');
  } else if (!chatId && agentId) {
    console.log('✅ Agent ID found without Chat ID - normal for new conversations');
  } else {
    console.log('✅ Both Chat ID and Agent ID found');
  }
  
  // Check localStorage
  console.log('\n2. LOCAL STORAGE:');
  const selectedAgent = localStorage.getItem('selectedAgent');
  const agents = localStorage.getItem('agents');
  
  console.log('Selected Agent:', selectedAgent ? 'Present' : 'Missing');
  console.log('Agents List:', agents ? 'Present' : 'Missing');
  
  if (selectedAgent) {
    try {
      const agentData = JSON.parse(selectedAgent);
      console.log('Selected Agent ID:', agentData.agentId?._id);
    } catch (e) {
      console.error('❌ Selected agent data is corrupted');
    }
  }
  
  if (agents) {
    try {
      const agentsData = JSON.parse(agents);
      console.log('Number of agents:', agentsData.length);
    } catch (e) {
      console.error('❌ Agents data is corrupted');
    }
  }
  
  // Check socket connection
  console.log('\n3. SOCKET CONNECTION:');
  
  // Try to access socket utilities
  if (window.socketRef || window.io) {
    console.log('Socket reference found');
  } else {
    console.log('No socket reference found in window');
  }
  
  // Check for React components
  console.log('\n4. REACT COMPONENTS:');
  const chatElements = document.querySelectorAll('[class*="chat"], [class*="Chat"]');
  console.log('Chat elements found:', chatElements.length);
  
  const inputElements = document.querySelectorAll('input[type="text"], textarea');
  console.log('Input elements found:', inputElements.length);
  
  if (inputElements.length > 0) {
    const messageInput = inputElements[0];
    console.log('Message input disabled:', messageInput.disabled);
  }
  
  // Check network connectivity
  console.log('\n5. NETWORK STATUS:');
  console.log('Online:', navigator.onLine);
  console.log('Connection type:', navigator.connection?.effectiveType || 'Unknown');
  
  // Test API connectivity
  console.log('\n6. API CONNECTIVITY TEST:');
  const apiUrl = 'https://api.dev.daiquiri.activate.bar';
  
  fetch(apiUrl + '/health', { method: 'GET' })
    .then(response => {
      console.log('✅ API Health Check:', response.status);
    })
    .catch(error => {
      console.error('❌ API Health Check Failed:', error.message);
    });
  
  // Check WebSocket connectivity
  console.log('\n7. WEBSOCKET CONNECTIVITY TEST:');
  try {
    const wsUrl = apiUrl.replace('https://', 'wss://').replace('http://', 'ws://');
    const testSocket = new WebSocket(wsUrl);
    
    testSocket.onopen = function() {
      console.log('✅ WebSocket connection successful');
      testSocket.close();
    };
    
    testSocket.onerror = function(error) {
      console.error('❌ WebSocket connection failed:', error);
    };
    
    testSocket.onclose = function(event) {
      console.log('WebSocket closed:', event.code, event.reason);
    };
    
    // Close after 5 seconds if still connecting
    setTimeout(() => {
      if (testSocket.readyState === WebSocket.CONNECTING) {
        console.warn('⚠️ WebSocket connection timeout');
        testSocket.close();
      }
    }, 5000);
    
  } catch (error) {
    console.error('❌ WebSocket test error:', error);
  }
  
  // Provide recommendations
  console.log('\n8. RECOMMENDATIONS:');
  
  const issues = [];
  const fixes = [];
  
  if (!chatId && !agentId) {
    issues.push('Missing URL parameters');
    fixes.push('Ensure URL contains either ?agentID=xxx or ?id=xxx&agentID=xxx');
  }
  
  if (!selectedAgent) {
    issues.push('No selected agent in localStorage');
    fixes.push('Select an agent before starting conversation');
  }
  
  if (chatElements.length === 0) {
    issues.push('Chat component not found');
    fixes.push('Check if React component is properly mounted');
  }
  
  if (issues.length === 0) {
    console.log('✅ No obvious issues detected');
    console.log('💡 Try the following:');
    console.log('   1. Open browser network tab and reload');
    console.log('   2. Check for JavaScript errors in console');
    console.log('   3. Verify socket.io connection in network tab');
  } else {
    console.log('❌ Issues detected:');
    issues.forEach((issue, i) => {
      console.log(`   ${i + 1}. ${issue}`);
    });
    console.log('\n💡 Suggested fixes:');
    fixes.forEach((fix, i) => {
      console.log(`   ${i + 1}. ${fix}`);
    });
  }
  
  console.log('\n=== DIAGNOSTIC COMPLETE ===');
  
  // Return diagnostic data for further analysis
  return {
    url: window.location.href,
    urlParams: { chatId, agentId },
    localStorage: { selectedAgent: !!selectedAgent, agents: !!agents },
    elements: { chatElements: chatElements.length, inputElements: inputElements.length },
    network: { online: navigator.onLine },
    issues,
    fixes
  };
})();
