<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket Test After Reload</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"] {
            width: 300px;
            padding: 5px;
            margin: 5px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Socket Connection Test After Page Reload</h1>
    
    <div id="status" class="status disconnected">
        Status: Disconnected
    </div>
    
    <div class="test-section">
        <h3>URL Parameters:</h3>
        <div id="urlParams"></div>
    </div>
    
    <div class="test-section">
        <h3>Connection Test:</h3>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="testRoomJoin()">Test Room Join</button>
        <button onclick="testMessageListener()">Test Message Listener</button>
        <button onclick="simulatePageReload()">Simulate Page Reload</button>
    </div>
    
    <div class="test-section">
        <h3>Message Test:</h3>
        <input type="text" id="testMessage" placeholder="Enter test message" value="Hello after reload">
        <button onclick="sendTestMessage()">Send Test Message</button>
    </div>
    
    <div class="test-section">
        <h3>Controls:</h3>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="exportLog()">Export Log</button>
    </div>
    
    <div>
        <h3>Log:</h3>
        <div id="log"></div>
    </div>

    <script>
        let socket = null;
        let roomId = null;
        let messageListenerActive = false;
        
        const API_BASE_URL = 'https://api.dev.daiquiri.activate.bar';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logDiv = document.getElementById('log');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `Status: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function testConnection() {
            log('=== TESTING CONNECTION ===', 'info');
            
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            
            updateStatus('Connecting...', 'connecting');
            
            socket = io(API_BASE_URL, {
                forceNew: true,
                reconnection: true,
                timeout: 20000
            });
            
            socket.on('connect', () => {
                log('✅ Socket connected successfully', 'success');
                updateStatus('Connected', 'connected');
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ Socket disconnected: ${reason}`, 'error');
                updateStatus('Disconnected', 'disconnected');
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`, 'error');
                updateStatus('Connection Error', 'disconnected');
            });
        }
        
        function testRoomJoin() {
            if (!socket || !socket.connected) {
                log('❌ Socket not connected. Connect first.', 'error');
                return;
            }
            
            if (!roomId) {
                log('❌ No room ID available', 'error');
                return;
            }
            
            log(`=== TESTING ROOM JOIN ===`, 'info');
            log(`Joining room: ${roomId}`, 'info');
            
            socket.emit('joinRoom', roomId);
            log('✅ Room join request sent', 'success');
            
            // Test if we can verify room membership
            setTimeout(() => {
                log('Room join verification completed', 'info');
            }, 1000);
        }
        
        function testMessageListener() {
            if (!socket || !socket.connected) {
                log('❌ Socket not connected. Connect first.', 'error');
                return;
            }
            
            log('=== TESTING MESSAGE LISTENER ===', 'info');
            
            if (messageListenerActive) {
                log('Message listener already active', 'warning');
                return;
            }
            
            socket.on('message', (data) => {
                log(`📨 Received message: ${JSON.stringify(data)}`, 'success');
            });
            
            messageListenerActive = true;
            log('✅ Message listener registered', 'success');
        }
        
        function sendTestMessage() {
            if (!socket || !socket.connected) {
                log('❌ Socket not connected. Connect first.', 'error');
                return;
            }
            
            if (!roomId) {
                log('❌ No room ID available', 'error');
                return;
            }
            
            const message = document.getElementById('testMessage').value;
            if (!message) {
                log('❌ No message to send', 'error');
                return;
            }
            
            log('=== SENDING TEST MESSAGE ===', 'info');
            log(`Message: ${message}`, 'info');
            log(`Room: ${roomId}`, 'info');
            
            const messageData = {
                data: {
                    inputs: [{
                        name: 'userInput',
                        type: 'longText',
                        data: message,
                        metaData: 'string'
                    }]
                },
                childId: roomId
            };
            
            socket.emit('message', messageData, (acknowledgment) => {
                log(`📤 Message acknowledgment: ${acknowledgment}`, 'success');
            });
            
            log('📤 Test message sent', 'info');
        }
        
        function simulatePageReload() {
            log('=== SIMULATING PAGE RELOAD ===', 'warning');
            log('Disconnecting socket...', 'info');
            
            if (socket) {
                socket.disconnect();
                socket = null;
                messageListenerActive = false;
            }
            
            updateStatus('Simulating Reload...', 'connecting');
            
            setTimeout(() => {
                log('Reconnecting after simulated reload...', 'info');
                testConnection();
                
                setTimeout(() => {
                    if (socket && socket.connected) {
                        testRoomJoin();
                        testMessageListener();
                    }
                }, 2000);
            }, 1000);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function exportLog() {
            const logContent = document.getElementById('log').innerHTML;
            const blob = new Blob([logContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `socket-test-log-${Date.now()}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // Initialize page
        function init() {
            const urlParams = new URLSearchParams(window.location.search);
            const chatId = urlParams.get('id');
            const agentId = urlParams.get('agentID');
            
            roomId = chatId;
            
            document.getElementById('urlParams').innerHTML = `
                <strong>Chat ID (Room):</strong> ${chatId || 'Not set'}<br>
                <strong>Agent ID:</strong> ${agentId || 'Not set'}
            `;
            
            log('=== PAGE INITIALIZED ===', 'info');
            log(`URL: ${window.location.href}`, 'info');
            log(`Room ID: ${roomId || 'None'}`, 'info');
            log(`Agent ID: ${agentId || 'None'}`, 'info');
            
            if (roomId) {
                log('Auto-starting connection test...', 'info');
                setTimeout(() => {
                    testConnection();
                    setTimeout(() => {
                        if (socket && socket.connected) {
                            testRoomJoin();
                            testMessageListener();
                        }
                    }, 2000);
                }, 500);
            } else {
                log('⚠️ No room ID in URL - some tests will not work', 'warning');
            }
        }
        
        // Initialize when page loads
        window.onload = init;
    </script>
</body>
</html>
