<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daiquiri Connection Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"] {
            width: 300px;
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>Daiquiri Socket Connection Test</h1>
    
    <div id="status" class="status disconnected">
        Status: Disconnected
    </div>
    
    <div>
        <h3>URL Parameters:</h3>
        <div id="urlParams"></div>
    </div>
    
    <div>
        <h3>Controls:</h3>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="testMessage()">Test Message</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div>
        <h3>Room Controls:</h3>
        <input type="text" id="roomInput" placeholder="Enter room ID">
        <button onclick="joinRoom()">Join Room</button>
    </div>
    
    <div>
        <h3>Message Test:</h3>
        <input type="text" id="messageInput" placeholder="Enter test message">
        <button onclick="sendMessage()">Send Message</button>
    </div>
    
    <div>
        <h3>Log:</h3>
        <div id="log"></div>
    </div>

    <script>
        let socket = null;
        let currentRoom = null;
        
        const API_BASE_URL = 'https://api.dev.daiquiri.activate.bar';
        
        function log(message) {
            const timestamp = new Date().toISOString();
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `Status: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function connect() {
            if (socket) {
                log('Already connected or connecting');
                return;
            }
            
            log(`Connecting to ${API_BASE_URL}...`);
            updateStatus('Connecting...', 'connecting');
            
            socket = io(API_BASE_URL, {
                forceNew: true,
                reconnection: true,
                timeout: 20000
            });
            
            socket.on('connect', () => {
                log('✅ Connected successfully');
                updateStatus('Connected', 'connected');
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ Disconnected: ${reason}`);
                updateStatus('Disconnected', 'disconnected');
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`);
                updateStatus('Connection Error', 'disconnected');
            });
            
            socket.on('message', (data) => {
                log(`📨 Received message: ${JSON.stringify(data)}`);
            });
            
            socket.on('error', (error) => {
                log(`❌ Socket error: ${error}`);
            });
            
            socket.on('heartbeat', () => {
                log('💓 Heartbeat received');
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('Disconnected');
                updateStatus('Disconnected', 'disconnected');
            }
        }
        
        function joinRoom() {
            const roomId = document.getElementById('roomInput').value;
            if (!roomId) {
                alert('Please enter a room ID');
                return;
            }
            
            if (!socket || !socket.connected) {
                alert('Not connected to socket');
                return;
            }
            
            currentRoom = roomId;
            socket.emit('joinRoom', roomId);
            log(`🚪 Joined room: ${roomId}`);
        }
        
        function sendMessage() {
            const message = document.getElementById('messageInput').value;
            if (!message) {
                alert('Please enter a message');
                return;
            }
            
            if (!socket || !socket.connected) {
                alert('Not connected to socket');
                return;
            }
            
            if (!currentRoom) {
                alert('Not in a room');
                return;
            }
            
            const messageData = {
                data: {
                    inputs: [{
                        name: 'userInput',
                        type: 'longText',
                        data: message,
                        metaData: 'string'
                    }]
                },
                childId: 'test-child-id'
            };
            
            socket.emit('message', messageData, (acknowledgment) => {
                log(`📤 Message sent, acknowledgment: ${acknowledgment}`);
            });
            
            log(`📤 Sent message: ${message}`);
            document.getElementById('messageInput').value = '';
        }
        
        function testMessage() {
            if (!socket || !socket.connected) {
                alert('Not connected to socket');
                return;
            }
            
            socket.emit('heartbeat');
            log('💓 Sent heartbeat');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initialize page
        function init() {
            const urlParams = new URLSearchParams(window.location.search);
            const chatId = urlParams.get('id');
            const agentId = urlParams.get('agentID');
            
            document.getElementById('urlParams').innerHTML = `
                <strong>Chat ID:</strong> ${chatId || 'Not set'}<br>
                <strong>Agent ID:</strong> ${agentId || 'Not set'}
            `;
            
            if (chatId) {
                document.getElementById('roomInput').value = chatId;
            }
            
            log('Page initialized');
            log(`URL: ${window.location.href}`);
            log(`Chat ID: ${chatId || 'None'}`);
            log(`Agent ID: ${agentId || 'None'}`);
        }
        
        // Auto-connect and join room if URL parameters are present
        function autoConnect() {
            const urlParams = new URLSearchParams(window.location.search);
            const chatId = urlParams.get('id');
            
            if (chatId) {
                log('Auto-connecting and joining room...');
                connect();
                
                // Wait for connection then join room
                setTimeout(() => {
                    if (socket && socket.connected) {
                        document.getElementById('roomInput').value = chatId;
                        joinRoom();
                    }
                }, 2000);
            }
        }
        
        // Initialize when page loads
        window.onload = function() {
            init();
            autoConnect();
        };
    </script>
</body>
</html>
