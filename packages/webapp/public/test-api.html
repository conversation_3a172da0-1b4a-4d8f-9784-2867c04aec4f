<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daiquiri API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input, textarea {
            width: 100%;
            padding: 5px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Daiquiri API Test</h1>
    
    <div id="status" class="status info">
        Ready to test API
    </div>
    
    <div>
        <h3>Configuration:</h3>
        <div>
            <label>API Base URL:</label>
            <input type="text" id="apiUrl" value="https://api.dev.daiquiri.activate.bar">
        </div>
        <div>
            <label>Organization ID:</label>
            <input type="text" id="orgId" value="67d17995cec95487f272df18">
        </div>
        <div>
            <label>Agent ID:</label>
            <input type="text" id="agentId" value="66fbec047b100b9f72d11f7f">
        </div>
        <div>
            <label>Test Message:</label>
            <textarea id="testMessage" rows="3">Hello, I need help with my profile.</textarea>
        </div>
    </div>
    
    <div>
        <h3>Tests:</h3>
        <button onclick="testApiHealth()">Test API Health</button>
        <button onclick="testStartChat()">Test Start Chat</button>
        <button onclick="testGetChat()">Test Get Chat</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div>
        <h3>Results:</h3>
        <div id="log"></div>
    </div>

    <script>
        let lastChatId = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logDiv = document.getElementById('log');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function testApiHealth() {
            const apiUrl = document.getElementById('apiUrl').value;
            log('Testing API health...', 'info');
            updateStatus('Testing API health...', 'info');
            
            try {
                const response = await fetch(`${apiUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`Health check response status: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.text();
                    log(`✅ API Health check passed: ${data}`, 'success');
                    updateStatus('API is healthy', 'success');
                } else {
                    log(`❌ API Health check failed: ${response.status}`, 'error');
                    updateStatus('API health check failed', 'error');
                }
            } catch (error) {
                log(`❌ API Health check error: ${error.message}`, 'error');
                updateStatus('API health check error', 'error');
            }
        }
        
        async function testStartChat() {
            const apiUrl = document.getElementById('apiUrl').value;
            const orgId = document.getElementById('orgId').value;
            const agentId = document.getElementById('agentId').value;
            const message = document.getElementById('testMessage').value;
            
            log('Testing start chat API...', 'info');
            updateStatus('Testing start chat...', 'info');
            
            const chatData = {
                inputs: [
                    {
                        name: 'userInput',
                        type: 'longText',
                        data: message,
                        metaData: 'string'
                    }
                ],
                agentId: agentId,
                organizationId: orgId,
                metaData: {
                    name: 'test-user'
                },
                callbackUrl: `${apiUrl}/v1/chat/agent`
            };
            
            log(`Chat data: ${JSON.stringify(chatData, null, 2)}`, 'info');
            
            try {
                const response = await fetch(`${apiUrl}/v1/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(chatData)
                });
                
                log(`Start chat response status: ${response.status}`, 'info');
                
                const responseData = await response.json();
                log(`Start chat response: ${JSON.stringify(responseData, null, 2)}`, 'info');
                
                if (response.ok && responseData.statusCode === 200) {
                    lastChatId = responseData.data._id;
                    log(`✅ Chat started successfully with ID: ${lastChatId}`, 'success');
                    updateStatus('Chat started successfully', 'success');
                    
                    // Update the get chat button
                    document.getElementById('getChatId').value = lastChatId;
                } else {
                    log(`❌ Start chat failed: ${JSON.stringify(responseData)}`, 'error');
                    updateStatus('Start chat failed', 'error');
                }
            } catch (error) {
                log(`❌ Start chat error: ${error.message}`, 'error');
                updateStatus('Start chat error', 'error');
            }
        }
        
        async function testGetChat() {
            const apiUrl = document.getElementById('apiUrl').value;
            const orgId = document.getElementById('orgId').value;
            const chatId = lastChatId || prompt('Enter chat ID:');
            
            if (!chatId) {
                log('❌ No chat ID provided', 'error');
                return;
            }
            
            log(`Testing get chat API for ID: ${chatId}...`, 'info');
            updateStatus('Testing get chat...', 'info');
            
            try {
                const response = await fetch(`${apiUrl}/v1/chat/${chatId}/${orgId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`Get chat response status: ${response.status}`, 'info');
                
                const responseData = await response.json();
                log(`Get chat response: ${JSON.stringify(responseData, null, 2)}`, 'info');
                
                if (response.ok) {
                    log(`✅ Chat retrieved successfully`, 'success');
                    updateStatus('Chat retrieved successfully', 'success');
                    
                    // Show message count
                    const messages = responseData.data || [];
                    log(`Found ${messages.length} messages in chat history`, 'info');
                } else {
                    log(`❌ Get chat failed: ${JSON.stringify(responseData)}`, 'error');
                    updateStatus('Get chat failed', 'error');
                }
            } catch (error) {
                log(`❌ Get chat error: ${error.message}`, 'error');
                updateStatus('Get chat error', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-fill from URL parameters
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const agentId = urlParams.get('agentID');
            const chatId = urlParams.get('id');
            
            if (agentId) {
                document.getElementById('agentId').value = agentId;
                log(`Agent ID from URL: ${agentId}`, 'info');
            }
            
            if (chatId) {
                lastChatId = chatId;
                log(`Chat ID from URL: ${chatId}`, 'info');
            }
        };
    </script>
</body>
</html>
