/**
 * Connection Debugger Utility
 * 
 * This utility helps debug socket connection issues after page reload
 */

import { getSocketStatus } from './socketUtils';

class ConnectionDebugger {
  constructor() {
    this.logs = [];
    this.isDebugging = false;
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      message,
      data,
      url: window.location.href,
      socketStatus: getSocketStatus()
    };
    
    this.logs.push(logEntry);
    console.log(`[CONNECTION DEBUG] ${timestamp}: ${message}`, data || '');
    
    // Keep only last 50 logs
    if (this.logs.length > 50) {
      this.logs = this.logs.slice(-50);
    }
  }

  startDebugging() {
    if (this.isDebugging) return;
    
    this.isDebugging = true;
    this.log('Starting connection debugging');
    
    // Monitor URL changes
    this.originalPushState = window.history.pushState;
    this.originalReplaceState = window.history.replaceState;
    
    window.history.pushState = (...args) => {
      this.log('URL changed via pushState', { newUrl: args[2] });
      return this.originalPushState.apply(window.history, args);
    };
    
    window.history.replaceState = (...args) => {
      this.log('URL changed via replaceState', { newUrl: args[2] });
      return this.originalReplaceState.apply(window.history, args);
    };
    
    // Monitor page visibility
    document.addEventListener('visibilitychange', () => {
      this.log('Page visibility changed', { hidden: document.hidden });
    });
    
    // Monitor network status
    window.addEventListener('online', () => {
      this.log('Network came online');
    });
    
    window.addEventListener('offline', () => {
      this.log('Network went offline');
    });
    
    // Periodic status check
    this.statusInterval = setInterval(() => {
      const status = getSocketStatus();
      if (!status.connected) {
        this.log('Periodic check: Socket disconnected', status);
      }
    }, 10000);
  }

  stopDebugging() {
    if (!this.isDebugging) return;
    
    this.isDebugging = false;
    this.log('Stopping connection debugging');
    
    // Restore original functions
    if (this.originalPushState) {
      window.history.pushState = this.originalPushState;
    }
    if (this.originalReplaceState) {
      window.history.replaceState = this.originalReplaceState;
    }
    
    // Clear interval
    if (this.statusInterval) {
      clearInterval(this.statusInterval);
    }
  }

  getDebugInfo() {
    const urlParams = new URLSearchParams(window.location.search);
    
    return {
      currentUrl: window.location.href,
      urlParams: {
        id: urlParams.get('id'),
        agentID: urlParams.get('agentID')
      },
      socketStatus: getSocketStatus(),
      localStorage: {
        selectedAgent: localStorage.getItem('selectedAgent'),
        agents: localStorage.getItem('agents')
      },
      recentLogs: this.logs.slice(-10)
    };
  }

  checkPageReloadIssue() {
    this.log('Checking page reload issue...');
    
    const urlParams = new URLSearchParams(window.location.search);
    const chatId = urlParams.get('id');
    const agentId = urlParams.get('agentID');
    
    const issues = [];
    
    if (!chatId && !agentId) {
      issues.push('No URL parameters found - history will be lost');
    }
    
    if (chatId && !agentId) {
      issues.push('Chat ID found but no agent ID - agent selection may be lost');
    }
    
    if (agentId && !chatId) {
      this.log('Agent ID found without chat ID - this is normal for new conversations');
    }
    
    const socketStatus = getSocketStatus();
    if (!socketStatus.connected) {
      issues.push('Socket not connected after page load');
    }
    
    if (socketStatus.currentRoomId !== chatId) {
      issues.push(`Room ID mismatch: URL has ${chatId}, socket has ${socketStatus.currentRoomId}`);
    }
    
    if (issues.length === 0) {
      this.log('✓ No page reload issues detected');
    } else {
      this.log('✗ Page reload issues detected:', issues);
    }
    
    return {
      hasIssues: issues.length > 0,
      issues,
      debugInfo: this.getDebugInfo()
    };
  }

  exportLogs() {
    const debugData = {
      timestamp: new Date().toISOString(),
      debugInfo: this.getDebugInfo(),
      allLogs: this.logs
    };
    
    const blob = new Blob([JSON.stringify(debugData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `connection-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Create global instance
const debugger = new ConnectionDebugger();

// Add to window for easy access in console
if (typeof window !== 'undefined') {
  window.connectionDebugger = debugger;
}

export default debugger;
