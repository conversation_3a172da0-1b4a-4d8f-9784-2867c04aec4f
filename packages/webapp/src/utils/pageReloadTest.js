/**
 * Page Reload Test Utility
 * 
 * This utility tests the page reload functionality to ensure:
 * 1. Socket connection is re-established after reload
 * 2. Agent ID and chat history are preserved
 * 3. Messages can be sent and received after reload
 */

import { getSocketStatus, connectSocket, joinRoom } from './socketUtils';
import connectionDebugger from './connectionDebugger';

class PageReloadTester {
  constructor() {
    this.testResults = [];
  }

  log(message, type = 'info', data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type, data };
    this.testResults.push(logEntry);
    console.log(`[RELOAD TEST ${type.toUpperCase()}] ${timestamp}: ${message}`, data || '');
  }

  async testURLPreservation() {
    this.log('Testing URL preservation after reload...', 'test');
    
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const agentId = urlParams.get('agentID');
      const chatId = urlParams.get('id');
      
      if (agentId) {
        this.log('✓ Agent ID preserved in URL', 'success', { agentId });
      } else {
        this.log('✗ Agent ID missing from URL', 'error');
        return false;
      }
      
      if (chatId) {
        this.log('✓ Chat ID preserved in URL', 'success', { chatId });
      } else {
        this.log('ℹ No chat ID in URL (normal for new conversations)', 'info');
      }
      
      return true;
    } catch (error) {
      this.log(`✗ URL preservation test error: ${error.message}`, 'error');
      return false;
    }
  }

  async testSocketConnection() {
    this.log('Testing socket connection after reload...', 'test');
    
    try {
      const status = getSocketStatus();
      this.log('Current socket status:', 'info', status);
      
      if (status.connected) {
        this.log('✓ Socket is connected', 'success');
        return true;
      } else {
        this.log('✗ Socket is not connected', 'error');
        
        // Try to reconnect
        this.log('Attempting to reconnect...', 'info');
        const urlParams = new URLSearchParams(window.location.search);
        const chatId = urlParams.get('id');
        
        if (chatId) {
          connectSocket(window.location.origin.replace(/^http/, 'ws'), true);
          joinRoom(chatId);
          
          // Wait a moment and check again
          await new Promise(resolve => setTimeout(resolve, 2000));
          const newStatus = getSocketStatus();
          
          if (newStatus.connected) {
            this.log('✓ Socket reconnected successfully', 'success');
            return true;
          } else {
            this.log('✗ Failed to reconnect socket', 'error');
            return false;
          }
        } else {
          this.log('ℹ No chat ID available for reconnection', 'info');
          return true; // Not an error for new conversations
        }
      }
    } catch (error) {
      this.log(`✗ Socket connection test error: ${error.message}`, 'error');
      return false;
    }
  }

  async testLocalStorageIntegrity() {
    this.log('Testing localStorage integrity...', 'test');
    
    try {
      const selectedAgent = localStorage.getItem('selectedAgent');
      const agents = localStorage.getItem('agents');
      
      if (selectedAgent) {
        try {
          const agentData = JSON.parse(selectedAgent);
          this.log('✓ Selected agent data preserved', 'success', { 
            agentId: agentData.agentId?._id 
          });
        } catch (parseError) {
          this.log('✗ Selected agent data corrupted', 'error');
          return false;
        }
      } else {
        this.log('ℹ No selected agent in localStorage', 'info');
      }
      
      if (agents) {
        try {
          const agentsData = JSON.parse(agents);
          this.log('✓ Agents list preserved', 'success', { 
            count: agentsData.length 
          });
        } catch (parseError) {
          this.log('✗ Agents list corrupted', 'error');
          return false;
        }
      } else {
        this.log('ℹ No agents list in localStorage', 'info');
      }
      
      return true;
    } catch (error) {
      this.log(`✗ localStorage test error: ${error.message}`, 'error');
      return false;
    }
  }

  async testComponentInitialization() {
    this.log('Testing component initialization...', 'test');
    
    try {
      // Check if the chat component is properly initialized
      const chatContainer = document.querySelector('.chat-body, .chat-container, [class*="chat"]');
      
      if (chatContainer) {
        this.log('✓ Chat component found in DOM', 'success');
      } else {
        this.log('✗ Chat component not found in DOM', 'error');
        return false;
      }
      
      // Check if input is enabled (indicates proper initialization)
      const messageInput = document.querySelector('input[type="text"], textarea');
      
      if (messageInput) {
        if (!messageInput.disabled) {
          this.log('✓ Message input is enabled', 'success');
        } else {
          this.log('⚠ Message input is disabled', 'warning');
        }
      } else {
        this.log('ℹ Message input not found', 'info');
      }
      
      return true;
    } catch (error) {
      this.log(`✗ Component initialization test error: ${error.message}`, 'error');
      return false;
    }
  }

  async runAllTests() {
    this.log('Starting page reload tests...', 'test');
    
    // Start debugging
    connectionDebugger.startDebugging();
    
    const tests = [
      { name: 'URL Preservation', test: () => this.testURLPreservation() },
      { name: 'Socket Connection', test: () => this.testSocketConnection() },
      { name: 'LocalStorage Integrity', test: () => this.testLocalStorageIntegrity() },
      { name: 'Component Initialization', test: () => this.testComponentInitialization() }
    ];
    
    const results = {};
    
    for (const { name, test } of tests) {
      try {
        results[name] = await test();
      } catch (error) {
        this.log(`Test "${name}" threw an error: ${error.message}`, 'error');
        results[name] = false;
      }
    }
    
    // Get debug info
    const debugInfo = connectionDebugger.checkPageReloadIssue();
    
    // Summary
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    this.log(`\n=== PAGE RELOAD TEST SUMMARY ===`, 'test');
    this.log(`Passed: ${passed}/${total}`, passed === total ? 'success' : 'error');
    
    Object.entries(results).forEach(([name, passed]) => {
      this.log(`${passed ? '✓' : '✗'} ${name}`, passed ? 'success' : 'error');
    });
    
    if (debugInfo.hasIssues) {
      this.log('Additional issues detected:', 'warning', debugInfo.issues);
    }
    
    return { 
      results, 
      passed, 
      total, 
      testResults: this.testResults,
      debugInfo 
    };
  }

  // Helper method to simulate page reload for testing
  simulatePageReload() {
    this.log('Simulating page reload...', 'test');
    
    // Save current state
    const currentUrl = window.location.href;
    
    // Reload the page
    window.location.reload();
    
    // Note: This will actually reload the page, so the test will continue after reload
  }
}

// Export for use in development/testing
export default PageReloadTester;

// Usage example:
// const tester = new PageReloadTester();
// tester.runAllTests().then(results => console.log('Page reload test results:', results));
