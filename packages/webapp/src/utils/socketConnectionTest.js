/**
 * Socket Connection Test Utility
 * 
 * This utility helps test the socket connection improvements made to fix:
 * 1. Agent output not returned to Daiquiri after prolonged waiting
 * 2. Page reload breaking agent-Spritz connection
 */

import { 
  connectSocket, 
  joinRoom, 
  emitMessage, 
  onMessage, 
  disconnectSocket,
  getSocketStatus,
  forceReconnect
} from './socketUtils';
import config from '../helper/config.json';

class SocketConnectionTester {
  constructor() {
    this.testResults = [];
    this.roomId = `test_room_${Date.now()}`;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type };
    this.testResults.push(logEntry);
    console.log(`[${type.toUpperCase()}] ${timestamp}: ${message}`);
  }

  async testBasicConnection() {
    this.log('Testing basic socket connection...', 'test');
    
    try {
      const socket = connectSocket(config.envVariables.API_BASE_URL);
      
      if (socket) {
        this.log('✓ Socket connection established', 'success');
        
        // Test room joining
        joinRoom(this.roomId);
        this.log(`✓ Joined room: ${this.roomId}`, 'success');
        
        return true;
      } else {
        this.log('✗ Failed to establish socket connection', 'error');
        return false;
      }
    } catch (error) {
      this.log(`✗ Connection error: ${error.message}`, 'error');
      return false;
    }
  }

  async testReconnectionAfterDisconnect() {
    this.log('Testing reconnection after disconnect...', 'test');
    
    try {
      // First establish connection
      await this.testBasicConnection();
      
      // Disconnect
      disconnectSocket();
      this.log('Socket disconnected', 'info');
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Test reconnection
      const socket = connectSocket(config.envVariables.API_BASE_URL, true);
      
      if (socket) {
        this.log('✓ Reconnection successful', 'success');
        joinRoom(this.roomId);
        return true;
      } else {
        this.log('✗ Reconnection failed', 'error');
        return false;
      }
    } catch (error) {
      this.log(`✗ Reconnection test error: ${error.message}`, 'error');
      return false;
    }
  }

  async testMessageAcknowledgment() {
    this.log('Testing message acknowledgment system...', 'test');
    
    return new Promise((resolve) => {
      let acknowledged = false;
      
      const testMessage = {
        inputs: [{
          name: 'userInput',
          type: 'longText',
          data: 'Test message for acknowledgment',
          metaData: 'string'
        }]
      };
      
      const timeout = setTimeout(() => {
        if (!acknowledged) {
          this.log('✗ Message acknowledgment timeout', 'error');
          resolve(false);
        }
      }, 10000);
      
      emitMessage(
        'message',
        { data: testMessage, childId: 'test_child' },
        this.roomId,
        null,
        (acknowledgment) => {
          acknowledged = true;
          clearTimeout(timeout);
          
          if (acknowledgment === 'Message received successfully') {
            this.log('✓ Message acknowledged successfully', 'success');
            resolve(true);
          } else {
            this.log(`✗ Message acknowledgment failed: ${acknowledgment}`, 'error');
            resolve(false);
          }
        }
      );
    });
  }

  async testConnectionStatus() {
    this.log('Testing connection status monitoring...', 'test');
    
    try {
      const status = getSocketStatus();
      this.log(`Connection status: ${JSON.stringify(status)}`, 'info');
      
      if (status.connected !== undefined && status.isConnecting !== undefined) {
        this.log('✓ Connection status monitoring working', 'success');
        return true;
      } else {
        this.log('✗ Connection status monitoring incomplete', 'error');
        return false;
      }
    } catch (error) {
      this.log(`✗ Connection status test error: ${error.message}`, 'error');
      return false;
    }
  }

  async testForceReconnect() {
    this.log('Testing force reconnect functionality...', 'test');
    
    try {
      const socket = await forceReconnect(this.roomId);
      
      if (socket) {
        this.log('✓ Force reconnect successful', 'success');
        return true;
      } else {
        this.log('✗ Force reconnect failed', 'error');
        return false;
      }
    } catch (error) {
      this.log(`✗ Force reconnect error: ${error.message}`, 'error');
      return false;
    }
  }

  async runAllTests() {
    this.log('Starting socket connection tests...', 'test');
    
    const tests = [
      { name: 'Basic Connection', test: () => this.testBasicConnection() },
      { name: 'Connection Status', test: () => this.testConnectionStatus() },
      { name: 'Force Reconnect', test: () => this.testForceReconnect() },
      { name: 'Reconnection After Disconnect', test: () => this.testReconnectionAfterDisconnect() },
      { name: 'Message Acknowledgment', test: () => this.testMessageAcknowledgment() }
    ];
    
    const results = {};
    
    for (const { name, test } of tests) {
      try {
        results[name] = await test();
      } catch (error) {
        this.log(`Test "${name}" threw an error: ${error.message}`, 'error');
        results[name] = false;
      }
    }
    
    // Summary
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    this.log(`\n=== TEST SUMMARY ===`, 'test');
    this.log(`Passed: ${passed}/${total}`, passed === total ? 'success' : 'error');
    
    Object.entries(results).forEach(([name, passed]) => {
      this.log(`${passed ? '✓' : '✗'} ${name}`, passed ? 'success' : 'error');
    });
    
    // Cleanup
    disconnectSocket();
    
    return { results, passed, total, testResults: this.testResults };
  }
}

// Export for use in development/testing
export default SocketConnectionTester;

// Usage example:
// const tester = new SocketConnectionTester();
// tester.runAllTests().then(results => console.log('Test results:', results));
