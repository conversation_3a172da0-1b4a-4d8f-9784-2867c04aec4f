import socketIOClient from 'socket.io-client';
import errorList from '../helper/errorList.json';
import config from '../helper/config.json';

const socketRef = {
  current: null,
  isConnecting: false,
  eventListeners: new Map(), // Track registered event listeners
  currentRoomId: null,
};

// Connect to the socket server with improved connection management
export const connectSocket = (url, forceReconnect = false) => {
  // Prevent multiple simultaneous connection attempts
  if (socketRef.isConnecting && !forceReconnect) {
    return socketRef.current;
  }

  // Clean up existing connection if forcing reconnect
  if (forceReconnect && socketRef.current) {
    socketRef.current.removeAllListeners();
    socketRef.current.disconnect();
    socketRef.current = null;
  }

  // Don't create new connection if one already exists and is connected
  if (socketRef.current && socketRef.current.connected && !forceReconnect) {
    return socketRef.current;
  }

  socketRef.isConnecting = true;

  try {
    socketRef.current = socketIOClient(url, {
      forceNew: forceReconnect,
      reconnection: true,
      reconnectionDelay: config.timeouts?.SOCKET_RECONNECTION_DELAY || 1000,
      reconnectionDelayMax: config.timeouts?.SOCKET_RECONNECTION_DELAY_MAX || 5000,
      maxReconnectionAttempts: config.timeouts?.SOCKET_MAX_RECONNECTION_ATTEMPTS || 5,
      timeout: config.timeouts?.SOCKET_CONNECTION_TIMEOUT || 20000,
    });

    // Add connection event listeners
    socketRef.current.on('connect', () => {
      socketRef.isConnecting = false;

      // Re-join room if we had one
      if (socketRef.currentRoomId) {
        socketRef.current.emit('joinRoom', socketRef.currentRoomId);
      }
    });

    socketRef.current.on('disconnect', () => {
      socketRef.isConnecting = false;
    });

    socketRef.current.on('connect_error', () => {
      socketRef.isConnecting = false;
    });

    // Re-register all previously registered event listeners
    socketRef.eventListeners.forEach((callback, event) => {
      socketRef.current.on(event, callback);
    });

    return socketRef.current;
  } catch (error) {
    socketRef.isConnecting = false;
    console.error('Failed to connect socket:', error);
    throw error;
  }
};

// Join a specific room using the room ID
export const joinRoom = (roomId) => {
  socketRef.currentRoomId = roomId;
  if (socketRef.current && socketRef.current.connected) {
    socketRef.current.emit('joinRoom', roomId);
  }
};

// Emit a message with improved connection handling
export const emitMessage = async (
  event,
  message,
  roomId,
  initializeSocket,
  callback
) => {
  // Ensure we have a connected socket
  if (!socketRef.current || !socketRef.current.connected) {
    if (initializeSocket) {
      await initializeSocket(roomId, true);
    } else {
      connectSocket(config.envVariables.API_BASE_URL, true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      joinRoom(roomId);
    }
  }

  // Ensure we're in the correct room
  if (socketRef.currentRoomId !== roomId) {
    joinRoom(roomId);
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  if (socketRef.current && socketRef.current.connected) {
    socketRef.current.emit(event, message, callback);
  } else {
    if (callback) {
      callback('Failed to send message: socket not connected');
    }
  }
};

// Register a handler for a specific event with tracking
export const onMessage = (event, callback) => {
  // Store the event listener for re-registration on reconnect
  socketRef.eventListeners.set(event, callback);

  if (socketRef.current) {
    // Remove existing listener to prevent duplicates
    socketRef.current.off(event);
    socketRef.current.on(event, callback);
  }
};

// Disconnect the socket with proper cleanup
export const disconnectSocket = () => {
  if (socketRef.current) {
    socketRef.current.removeAllListeners();
    socketRef.current.disconnect();
    socketRef.current = null;
  }
  socketRef.eventListeners.clear();
  socketRef.currentRoomId = null;
  socketRef.isConnecting = false;
};

// Emit a heartbeat event
export const emitHeartbeat = () => {
  if (socketRef.current && socketRef.current.connected) {
    socketRef.current.emit('heartbeat');
  }
};

// Get current socket connection status
export const getSocketStatus = () => {
  return {
    connected: socketRef.current?.connected || false,
    isConnecting: socketRef.isConnecting,
    currentRoomId: socketRef.currentRoomId,
    hasEventListeners: socketRef.eventListeners.size > 0,
  };
};

// Force reconnect with all state restoration
export const forceReconnect = async (roomId) => {
  console.log('Forcing socket reconnection...');
  try {
    const socket = connectSocket(config.envVariables.API_BASE_URL, true);
    if (roomId) {
      joinRoom(roomId);
    }
    return socket;
  } catch (error) {
    console.error('Force reconnect failed:', error);
    throw error;
  }
};

// Attach heartbeat and reconnection logic with improvements
export const setupHeartbeatAndReconnect = (setError, widgetCallBack) => {
  let retryCount = 0;
  let heartbeatInterval = null;
  let reconnectTimeout = null;
  let lastHeartbeatResponse = Date.now();
  const maxRetries = config.timeouts.SOCKET_MAX_RECONNECTION_ATTEMPTS || 5;
  const heartbeatIntervalMs = config.timeouts.HEARTBEAT_INTERVAL || 10000;
  const connectionRecoveryWindow = config.timeouts.CONNECTION_RECOVERY_WINDOW || 30000;

  const reconnect = async () => {
    if (retryCount < maxRetries) {
      retryCount++;
      console.log(`Attempting reconnection ${retryCount}/${maxRetries}...`);

      try {
        await forceReconnect(socketRef.currentRoomId);
        retryCount = 0; // Reset on successful reconnection
        lastHeartbeatResponse = Date.now();
        setError(null);
      } catch (error) {
        console.error(`Reconnection attempt ${retryCount} failed:`, error);

        if (retryCount >= maxRetries) {
          if (widgetCallBack) {
            widgetCallBack(errorList.serverNotResponding, null);
          }
          setError('Unable to reconnect to the server after multiple attempts.');
        } else {
          // Schedule next retry with exponential backoff
          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
          reconnectTimeout = setTimeout(reconnect, delay);
        }
      }
    }
  };

  const handleDisconnect = (reason) => {
    console.log('Socket disconnected:', reason);

    // Clear existing intervals/timeouts
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }

    if (widgetCallBack) {
      widgetCallBack(errorList.socketConnectionBreaks, null);
    }
    setError('Socket connection lost. Attempting to reconnect...');

    // Start reconnection process
    reconnectTimeout = setTimeout(reconnect, 1000);
  };

  const handleHeartbeatResponse = () => {
    lastHeartbeatResponse = Date.now();
    retryCount = 0; // Reset retry count on successful heartbeat
    setError(null); // Clear any previous errors
  };

  const checkHeartbeatTimeout = () => {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - lastHeartbeatResponse;

    if (timeSinceLastHeartbeat > connectionRecoveryWindow) {
      console.log('Heartbeat timeout detected, forcing reconnection...');
      handleDisconnect('heartbeat_timeout');
    }
  };

  // Start heartbeat interval
  heartbeatInterval = setInterval(() => {
    emitHeartbeat();
    checkHeartbeatTimeout();
  }, heartbeatIntervalMs);

  // Set up socket event listeners if socket exists
  if (socketRef.current) {
    socketRef.current.on('disconnect', handleDisconnect);
    socketRef.current.on('connect', () => {
      console.log('Socket reconnected successfully');
      retryCount = 0;
      lastHeartbeatResponse = Date.now();
      setError(null);

      // Rejoin room if we have one
      if (socketRef.currentRoomId) {
        joinRoom(socketRef.currentRoomId);
      }
    });
  }

  // Cleanup function
  const cleanup = () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }
    if (socketRef.current) {
      socketRef.current.off('disconnect', handleDisconnect);
    }
  };

  return {
    handleDisconnect,
    handleHeartbeatResponse,
    heartbeatInterval,
    cleanup
  };
};
