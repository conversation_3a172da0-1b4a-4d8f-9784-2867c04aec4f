/**
 * Simple test to verify that the chatbox component can be imported without circular reference errors
 */

import React from 'react';

// Test import - this should not throw a "Cannot access before initialization" error
try {
  const ChatUI = require('./chatbox.js').default;
  console.log('✓ ChatUI component imported successfully');
  
  // Test that the component can be instantiated (basic smoke test)
  const testProps = {
    customStyle: {},
    widgetCallBack: () => {},
    organizationId: 'test-org',
    agentId: 'test-agent',
    discardCallBack: () => {},
    discardButtonText: 'Discard',
    callbackButtonText: 'Callback',
    selectedAgentWidget: 'Test widget'
  };
  
  // This should not throw an error
  const element = React.createElement(ChatUI, testProps);
  console.log('✓ ChatUI component can be instantiated');
  
  console.log('✓ All tests passed - circular reference issue resolved');
  
} catch (error) {
  console.error('✗ Test failed:', error.message);
  console.error('Stack trace:', error.stack);
}

export default null; // This is just a test file
