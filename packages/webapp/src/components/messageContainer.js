import React, { useState, useMemo } from 'react';
import DOMPurify from 'dompurify';
import PropTypes from 'prop-types';
import ClipboardIcon from '../../public/images/copy';
import '../styles/chatui.css';
import config from '../helper/config.json';

function Messages({
  message,
  customStyles,
  widgetCallBack,
  discardCallBack,
  discardButtonText,
  callbackButtonText,
}) {
  const [copyMsg, setCopyMsg] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const isWidget = config.envVariables.WIDGET === 'yes';

  // Define hover styles only if provided
  const hoverButtonStyle = customStyles?.messageHeader?.button?.hover || {};

  const handleCopy = (message) => {
    navigator.clipboard
      .writeText(message.text)
      .then(() => {
        setCopyMsg(true);
        setTimeout(() => {
          setCopyMsg(false);
        }, 2000);
      })
      .catch((err) => {
        console.error('Failed to copy text: ', err);
      });
  };

  const hasHtml = (str) => /<[^>]+>/i.test(str);
  const isHtmlContent = hasHtml(message.text || '');

  // Memoized sanitized HTML so that the expensive DOMPurify execution
  // only runs when the message text actually changes. This ensures iframe
  // embeds do not get destroyed on unrelated re-renders (e.g. user typing
  // in the input box).
  const sanitizedHtml = useMemo(() => {
    if (!isHtmlContent) return null;

    return DOMPurify.sanitize(message.text, {
      ADD_TAGS: ['iframe'],
      ADD_ATTR: [
        'allow',
        'allowfullscreen',
        'frameborder',
        'src',
        'width',
        'height',
        'loading',
        'referrerpolicy',
        'title',
      ],
    });
  }, [isHtmlContent, message.text]);

  const renderContent = () => {
    if (isHtmlContent && sanitizedHtml) {
      return <span dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
    }

    const lines = (message.text || '').split(/\n/);
    return lines.map((line, idx) => (
      <React.Fragment key={idx}>
        {line}
        {idx < lines.length - 1 && <br />}
      </React.Fragment>
    ));
  };

  return (
    <div
      key={message.text}
      className={`message-container ${message.type}`}
      style={customStyles?.messageContainer?.[message.type]}
    >
      <div
        className={`message ${message.type}`}
        style={{
          ...customStyles?.message?.[message.type],
          display: 'inline-block',
        }}
      >
        {renderContent()}
      </div>

      {message.type === 'received' && message.isCompleted && isWidget &&
        widgetCallBack && discardCallBack && discardButtonText && callbackButtonText && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '10px',
            }}
          >
            <button
              className="sendButton"
              style={{
                ...customStyles?.discardButton,
                marginRight: '5px',
                display: 'block',
                flex: 1,
              }}
              onClick={() => discardCallBack(message.text)}
            >
              {discardButtonText ? discardButtonText : 'Discard'}
            </button>
            <button
              style={{
                ...customStyles?.callbackButton,
                marginLeft: '5px',
                display: 'block',
                flex: 1,
              }}
              className="sendButton"
              onClick={() => widgetCallBack(null, message.text)}
            >
              {callbackButtonText ? callbackButtonText : 'Use It'}
            </button>
          </div>
        )}
    </div>
  );
}

Messages.propTypes = {
  widgetCallBack: PropTypes.func.optional,
  message: PropTypes.object,
  customStyles: PropTypes.object,
  discardCallBack: PropTypes.func.isRequired,
  discardButtonText: PropTypes.string,
  callbackButtonText: PropTypes.string,
};
export default React.memo(Messages);
