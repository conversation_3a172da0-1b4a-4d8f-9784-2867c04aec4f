/**
 * Test to verify circular reference issues are resolved
 */

import React from 'react';

// Test import - this should not throw a "Cannot access before initialization" error
console.log('Testing chatbox import...');

try {
  // Import the component
  const ChatUI = require('./chatbox.js').default;
  console.log('✅ ChatUI component imported successfully');
  
  // Test that the component can be instantiated (basic smoke test)
  const testProps = {
    customStyle: {},
    widgetCallBack: () => {},
    organizationId: 'test-org',
    agentId: 'test-agent',
    discardCallBack: () => {},
    discardButtonText: 'Discard',
    callbackButtonText: 'Callback',
    selectedAgentWidget: 'Test widget'
  };
  
  // This should not throw an error
  const element = React.createElement(ChatUI, testProps);
  console.log('✅ ChatUI component can be instantiated');
  console.log('✅ All circular reference tests passed');
  
} catch (error) {
  console.error('✗ Circular reference test failed:', error.message);
  console.error('Stack trace:', error.stack);
  
  // Check if it's specifically a circular reference error
  if (error.message.includes('Cannot access') && error.message.includes('before initialization')) {
    console.error('❌ CIRCULAR REFERENCE ERROR DETECTED');
    console.error('This indicates that functions are being referenced before they are defined');
  }
}

export default null; // This is just a test file
