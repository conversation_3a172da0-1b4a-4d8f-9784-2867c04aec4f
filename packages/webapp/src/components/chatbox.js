'use client';
import React from 'react';
import {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useCallback,
} from 'react';
import { get } from 'lodash';
import ChatUIComponent from '@/components/chatUIComponent';
import { ChatAPI } from '../api';
import '../styles/global.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import {
  connectSocket,
  joinRoom,
  emitMessage,
  onMessage,
  disconnectSocket,
  setupHeartbeatAndReconnect,
  getSocketStatus,
} from '../utils/socketUtils';

import '../styles/chatui.css';
import defaultCustomStyles from '@/helper/customStyle';

import { getLocalStorageItem, setLocalStorageItem } from '@/utils/storage';
import config from '../helper/config.json';
import errorList from '../helper/errorList.json';

const ChatUI = forwardRef(
  (
    {
      customStyle,
      widgetCallBack,
      organizationId,
      agentId,
      discardCallBack,
      discardButtonText,
      callbackButtonText,
      selectedAgentWidget,
    },
    ref
  ) => {
    const ORGANIZATION_ID = organizationId
      ? organizationId
      : config.envVariables.ORGANIZATION_ID;
    const selectAgent = getLocalStorageItem('selectedAgent');

    const [messages, setMessages] = useState([
      {
        text: get(selectAgent, 'agentId.description', selectedAgentWidget),
        type: 'received',
        id: '',
      },
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const [error, setError] = useState(null);
    const [messageHistory, setMessageHistory] = useState([]);
    const [agents, setAgents] = useState([]);
    const [isInputDisabled, setIsInputDisabled] = useState(false);
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [selectedAgent, setSelectedAgent] = useState({});
    const [childId, setChildId] = useState('');
    const [roomId, setRoomId] = useState('');
    const [pendingMessages, setPendingMessages] = useState(new Map()); // Track pending messages
    const [connectionStatus, setConnectionStatus] = useState('connecting');
    const [responseTimeout, setResponseTimeout] = useState(null); // Track response timeout
    const customStyles = customStyle ? customStyle : defaultCustomStyles;

    const handleMessage = (message) => {
      // Clear response timeout since we received a message
      if (responseTimeout) {
        clearTimeout(responseTimeout);
        setResponseTimeout(null);
      }

      // Clear any pending message that matches this response
      if (message._id || message.rootJobId) {
        const messageId = message._id || message.rootJobId;
        setPendingMessages(prev => {
          const newMap = new Map(prev);
          newMap.delete(messageId);
          return newMap;
        });
      }

      try {
        setChildId(message._id);

        const messageData =
          message.status === 'awaitingInput'
            ? message.inputs
            : message.successResp?.outputs;

        if (!messageData) {
          setError('Invalid response format received');
          setIsTyping(false);
          return;
        }

        const updatedDataArray = messageData.map(
          ({ _id, validation, label, ...rest }) => rest
        );
        const updatedData = updatedDataArray.map((item) => ({
          ...item,
          metaData: 'string',
        }));

        setMessageHistory(updatedData);

        // Store messageHistory in localStorage for persistence across page reloads
        if (updatedData.length > 0) {
          setLocalStorageItem(`messageHistory_${message._id || roomId}`, updatedData);
        }

        setIsTyping(false);

        let data = {};
        if (updatedData.length > 2) {
          data = { text: updatedData[1].data, type: 'received' };
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
        } else if (updatedData.length === 2) {
          data = {
            text: updatedData[1].data,
            type: 'received',
            isCompleted: true,
          };
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
        } else {
          data = { text: 'Response received', type: 'received' };
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
        }

        setMessages((prevMessages) => [...prevMessages, data]);
        setError(null);
        setConnectionStatus('connected');
      } catch (error) {
        console.error('Error processing message:', error);
        setError('Error processing response');
        setIsTyping(false);
      }
    };



    // Function to retry pending messages (will be defined after initializeSocket)
    const retryPendingMessages = useCallback(() => {
      if (pendingMessages.size === 0) return;

      pendingMessages.forEach((messageData, messageId) => {
        const { event, message, roomId: msgRoomId, callback } = messageData;

        emitMessage(event, message, msgRoomId, null, (acknowledgment) => {
          if (acknowledgment === 'Message received successfully') {
            // Remove from pending messages on success
            setPendingMessages(prev => {
              const newMap = new Map(prev);
              newMap.delete(messageId);
              return newMap;
            });

            if (callback) {
              callback(acknowledgment);
            }
          } else {
            console.error(`Failed to retry message ${messageId}:`, acknowledgment);
          }
        });
      });
    }, [pendingMessages]);

    // Function to get history messages - defined before initializeSocket


    const getHistoryMessages = useCallback(async (id) => {
      try {
        // First, try to load messageHistory from localStorage
        const storedMessageHistory = getLocalStorageItem(`messageHistory_${id}`);

        if (storedMessageHistory && Array.isArray(storedMessageHistory) && storedMessageHistory.length > 0) {
          setMessageHistory(storedMessageHistory);
        }

        // Get UI messages for display
        const resp = await ChatAPI.getChat(ORGANIZATION_ID, id);
        const messageResp = get(resp, 'data', []);

        const filteredData = messageResp.filter((item) =>
          item.hasOwnProperty('text')
        );

        if (filteredData.length > 0) {
          setChildId(filteredData[filteredData.length - 1].id);
          setMessages(prevMessages => [prevMessages[0], ...filteredData]);
        }

        // If no stored messageHistory was found, initialize a basic one
        if (!storedMessageHistory || storedMessageHistory.length === 0) {
          const basicMessageHistory = [
            {
              name: 'userInput',
              type: 'longText',
              data: '',
              metaData: 'string',
            }
          ];
          setMessageHistory(basicMessageHistory);
        }
      } catch (error) {
        console.error('Failed to get history messages:', error);
        setError('Failed to load chat history');

        // Initialize basic messageHistory even on error
        const basicMessageHistory = [
          {
            name: 'userInput',
            type: 'longText',
            data: '',
            metaData: 'string',
          }
        ];
        setMessageHistory(basicMessageHistory);
      }
    }, [ORGANIZATION_ID, setChildId, setMessages, setError, setMessageHistory]);

    // Memoized socket initialization function with improved error handling
    const initializeSocket = useCallback(
      (id, disconnected = false) => {
        try {
          connectSocket(config.envVariables.API_BASE_URL, disconnected);
          setConnectionStatus('connecting');

          const { handleHeartbeatResponse, cleanup } =
            setupHeartbeatAndReconnect(setError, widgetCallBack);

          const handleError = (errorMessage) => {
            setError(errorMessage);
            setConnectionStatus('error');

            // Implement retry logic for failed messages
            if (errorMessage === 'Failed to send message' || errorMessage.includes('socket not connected')) {
              setTimeout(() => {
                connectSocket(config.envVariables.API_BASE_URL, true);
                if (id) {
                  joinRoom(id);
                }
                retryPendingMessages();
              }, 2000);
            }
          };

          const handleConnect = () => {
            setConnectionStatus('connected');
            setError(null);
            retryPendingMessages();
          };

          const handleReconnect = () => {
            setConnectionStatus('connected');
            setError(null);

            if (id) {
              joinRoom(id);
              setTimeout(() => retryPendingMessages(), 500);
            }
          };

          // Attach socket listeners
          onMessage('message', handleMessage);
          onMessage('error', handleError);
          onMessage('heartbeat', handleHeartbeatResponse);
          onMessage('connect', handleConnect);
          onMessage('reconnect', handleReconnect);

          // Join room and get messages with proper sequencing
          const socketStatus = getSocketStatus();
          if (socketStatus.connected) {
            joinRoom(id);

            setTimeout(() => {
              if (!disconnected) {
                getHistoryMessages(id);
              }
            }, 500);
          } else {
            // If not connected yet, wait for connection
            const waitForConnection = () => {
              const status = getSocketStatus();
              if (status.connected) {
                joinRoom(id);

                setTimeout(() => {
                  if (!disconnected) {
                    getHistoryMessages(id);
                  }
                }, 500);
              } else {
                setTimeout(waitForConnection, 500);
              }
            };
            waitForConnection();
          }

          setConnectionStatus('connected');

          // Return cleanup function
          return () => {
            cleanup();
            disconnectSocket();
          };
        } catch (error) {
          console.error('Failed to initialize socket:', error);
          setError('Failed to establish connection');
          setConnectionStatus('error');
          throw error;
        }
      },
      [setError, widgetCallBack, retryPendingMessages, handleMessage, getHistoryMessages]
    );

    // Main initialization effect - runs once on mount
    useEffect(() => {
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlChatId = urlParams.get('id');

        if (urlChatId) {
          setTimeout(() => {
            setRoomId(urlChatId);
          }, 100);
        } else {
          // Initialize basic messageHistory for new conversations
          const basicMessageHistory = [
            {
              name: 'userInput',
              type: 'longText',
              data: '',
              metaData: 'string',
            }
          ];
          setMessageHistory(basicMessageHistory);
        }
      }
    }, []);

    // Socket initialization effect - runs when roomId changes
    useEffect(() => {
      if (!roomId) {
        return;
      }

      let cleanup;
      let retryCount = 0;
      const maxRetries = 3;

      const attemptConnection = () => {
        try {
          cleanup = initializeSocket(roomId);

          // Verify connection after a short delay
          setTimeout(() => {
            const status = getSocketStatus();

            if (!status.connected && retryCount < maxRetries) {
              retryCount++;
              setTimeout(attemptConnection, 1000 * retryCount);
            } else if (!status.connected) {
              setError('Connection failed. Please refresh the page.');
            }
          }, 1000);

        } catch (error) {
          console.error('Socket initialization failed:', error);
          if (retryCount < maxRetries) {
            retryCount++;
            setTimeout(attemptConnection, 1000 * retryCount);
          } else {
            setError('Failed to establish connection. Please refresh the page.');
          }
        }
      };

      const timer = setTimeout(attemptConnection, 300);

      return () => {
        clearTimeout(timer);
        if (cleanup) {
          cleanup();
        }
      };
    }, [roomId]);

    // Handle page visibility changes and connection recovery
    useEffect(() => {
      const handleVisibilityChange = () => {
        if (!document.hidden && roomId) {
          const status = getSocketStatus();
          if (!status.connected) {
            setTimeout(() => {
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(roomId);
              getHistoryMessages(roomId);
            }, 1000);
          }
        }
      };

      const handleOnline = () => {
        if (roomId) {
          setTimeout(() => {
            connectSocket(config.envVariables.API_BASE_URL, true);
            joinRoom(roomId);
            getHistoryMessages(roomId);
          }, 1000);
        }
      };

      const handleOffline = () => {
        setConnectionStatus('offline');
        setError('Network connection lost. Will reconnect automatically.');
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }, [roomId]);

    // Periodic connection health check
    useEffect(() => {
      if (!roomId) return;

      const healthCheckInterval = setInterval(() => {
        const status = getSocketStatus();

        if (!status.connected && connectionStatus !== 'offline') {
          setConnectionStatus('reconnecting');
          connectSocket(config.envVariables.API_BASE_URL, true);
          joinRoom(roomId);
        }
      }, 30000); // Check every 30 seconds

      return () => clearInterval(healthCheckInterval);
    }, [roomId, connectionStatus]);

    // Initialize agents and selected agent on mount
    useEffect(() => {
      const agent = getLocalStorageItem('agents');
      const selectedAgent = getLocalStorageItem('selectedAgent');

      if (agent) {
        setAgents(agent);
      }

      // Check URL for agent selection on page load
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlAgentId = urlParams.get('agentID');

        if (urlAgentId && agent) {
          const agentFromUrl = agent.find(a => get(a, 'agentId._id') === urlAgentId);
          if (agentFromUrl) {
            setSelectedAgent(agentFromUrl);
            setLocalStorageItem('selectedAgent', agentFromUrl);
          } else {
            if (selectedAgent) {
              setSelectedAgent(selectedAgent);
            }
          }
        } else if (selectedAgent) {
          setSelectedAgent(selectedAgent);
        }
      } else if (selectedAgent) {
        setSelectedAgent(selectedAgent);
      }
    }, []);



    const handleSelectAgent = (agent) => {
      setLocalStorageItem('selectedAgent', agent);
      setSelectedAgent(agent);

      if (typeof window !== 'undefined') {
        const url = new URL(window.location);

        // Clear any existing chat session ID since we're selecting a new agent
        url.searchParams.delete('id');
        // Set the agent ID parameter
        url.searchParams.set('agentID', agent.agentId._id);

        // Update the URL without reloading the page
        window.history.replaceState(window.history.state, '', url);

        // Reset messages for new agent
        setMessages([
          {
            text: get(
              agent,
              'agentId.description',
              'Welcome! how can i assist you ?'
            ),
            type: 'received',
            id: '',
          },
        ]);
        setIsTyping(false);
        setIsInputDisabled(false);
        setIsButtonDisabled(false);

        // Clear room ID to start fresh conversation
        setRoomId('');
      }
    };

    if (config.envVariables.WIDGET === 'no') {
      useImperativeHandle(ref, () => ({
        handleSelectAgent,
      }));
    }
    useImperativeHandle(ref, () => ({
      sendMessage,
      setInputMessage,
    }));
    const fetchData = async (inputMessage = 'Write my Profile for chef') => {
      setMessages([...messages, { text: inputMessage, type: 'sent' }]);
      let selectedAgentId = '';
      setIsTyping(true);
      setIsInputDisabled(true);
      setIsButtonDisabled(true);

      const isWidget = config.envVariables.WIDGET;
      const selectedAgent = getLocalStorageItem('selectedAgent');

      // Check URL for agentID parameter first (for non-widget mode)
      if (isWidget === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlAgentId = urlParams.get('agentID');

        if (urlAgentId) {
          selectedAgentId = urlAgentId;
        } else if (get(selectedAgent, 'agentId._id', '') !== '') {
          selectedAgentId = get(selectedAgent, 'agentId._id', '');
        } else {
          selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
        }
      } else if (isWidget === 'yes' || get(selectedAgent, 'agentId._id', '') === '') {
        selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
      } else {
        selectedAgentId = get(selectedAgent, 'agentId._id', '');
      }
      const data = {
        inputs: [
          {
            name: 'userInput',
            type: 'longText',
            data: inputMessage,
            metaData: 'string',
          },
        ],
        agentId: selectedAgentId,
        organizationId: organizationId
          ? organizationId
          : config.envVariables.ORGANIZATION_ID,
        metaData: {
          name: 'sachin',
        },
        callbackUrl: config.envVariables.CALL_BACK_URL,
      };
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      try {
        if (!id) {
          const response = await ChatAPI.startChat(data);

          if (response.statusCode === 200) {
            const id = response.data._id;

            setRoomId(id);
            setChildId(id);

            if (
              typeof window !== 'undefined' &&
              config.envVariables.WIDGET === 'no'
            ) {
              const url = new URL(window.location);
              url.searchParams.set('id', id);
              const currentAgentId = url.searchParams.get('agentID');
              if (currentAgentId) {
                url.searchParams.set('agentID', currentAgentId);
              }
              window.history.replaceState(window.history.state, '', url);
            }

            connectSocket(config.envVariables.API_BASE_URL);
            joinRoom(id);
            onMessage('message', handleMessage);

          } else {
            setIsTyping(false);
            setIsInputDisabled(false);
            setIsButtonDisabled(false);
            setError(`Failed to start chat (${response.statusCode})`);
            if (widgetCallBack) {
              widgetCallBack(errorList.serverNotResponding, null);
            }
          }
        }
      } catch (error) {
        console.error('Start chat API error:', error);
        setIsTyping(false);
        setIsInputDisabled(false);
        setIsButtonDisabled(false);
        setError(`Network error: ${error.message}`);
      }
    };

    const sendMessage = async () => {
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }

      if (inputMessage.trim() !== '') {
        if (!id) {
          await fetchData(inputMessage);
        } else {
          // Ensure messageHistory has the correct structure
          let updatedMessageHistory = [...messageHistory];

          const index = updatedMessageHistory.findIndex(
            (item) => item.name === 'userInput'
          );

          if (index !== -1) {
            // Update existing userInput
            updatedMessageHistory[index].data = inputMessage;
          } else {
            // Create new userInput if not found (this happens after page reload)
            updatedMessageHistory = [
              {
                name: 'userInput',
                type: 'longText',
                data: inputMessage,
                metaData: 'string',
              },
              ...updatedMessageHistory
            ];
          }

          const data = {
            inputs: updatedMessageHistory,
          };

          // Update the messageHistory state with the corrected data
          setMessageHistory(updatedMessageHistory);

          // Store updated messageHistory in localStorage
          setLocalStorageItem(`messageHistory_${id}`, updatedMessageHistory);

          const messageId = `${Date.now()}_${Math.random()}`;
          const messageData = {
            event: 'message',
            message: { data, childId },
            roomId: id,
            callback: (acknowledgment) => {
              if (acknowledgment === 'Message received successfully') {
                // Remove from pending messages
                setPendingMessages(prev => {
                  const newMap = new Map(prev);
                  newMap.delete(messageId);
                  return newMap;
                });
              } else {
                console.error('Message failed:', acknowledgment);
                setError('Failed to send message. Will retry automatically.');
              }
            }
          };

          // Add to pending messages before sending
          setPendingMessages(prev => new Map(prev.set(messageId, messageData)));

          emitMessage(
            'message',
            { data, childId },
            id,
            null, // Remove circular reference
            messageData.callback
          );


          // Set up response timeout
          const timeout = setTimeout(() => {
            setError('Agent is taking longer than expected. Checking connection...');

            // Check socket status and attempt reconnection if needed
            const status = getSocketStatus();
            if (!status.connected) {
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(id);
            } else {
              // Socket is connected but no response, this might be the agent taking time
              setError('Agent is processing your request. This may take a moment...');
            }
          }, config.timeouts.AGENT_RESPONSE_TIMEOUT || 120000); // 2 minutes default

          setResponseTimeout(timeout);

          setIsTyping(true);
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
          setError(null);
          setMessages([
            ...messages,
            { text: inputMessage, type: 'sent', id: '' },
          ]);
          setInputMessage('');
        }
      }
    };



    const handleInputChange = (event) => {
      setInputMessage(event.target.value);
    };

    function smoothScrollToBottom(element) {
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;
      const maxScrollTop = scrollHeight - clientHeight;
      const currentScrollTop = element.scrollTop;
      const targetScrollTop = Math.min(
        maxScrollTop,
        currentScrollTop + clientHeight
      );
      const distance = targetScrollTop - currentScrollTop;
      const duration = 300; // Adjust the duration as needed

      let start = null;
      function step(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const increment = (distance * progress) / duration;
        element.scrollTo(0, currentScrollTop + increment);
        if (progress < duration) {
          requestAnimationFrame(step);
        }
      }
      requestAnimationFrame(step);
    }

    return (
      <ChatUIComponent
        agents={agents}
        messages={messages}
        handleInputChange={handleInputChange}
        sendMessage={sendMessage}
        customStyles={customStyles}
        smoothScrollToBottom={smoothScrollToBottom}
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        isTyping={isTyping}
        isInputDisabled={isInputDisabled}
        selectedAgent={selectedAgent}
        handleSelectAgent={handleSelectAgent}
        widgetCallBack={widgetCallBack}
        discardCallBack={discardCallBack}
        discardButtonText={discardButtonText}
        callbackButtonText={callbackButtonText}
        isButtonDisabled={isButtonDisabled}
      />
    );
  }
);

export default ChatUI;
