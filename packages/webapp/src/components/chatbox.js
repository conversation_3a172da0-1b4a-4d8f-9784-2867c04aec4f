'use client';
import React from 'react';
import {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useCallback,
} from 'react';
import { get } from 'lodash';
import ChatUIComponent from '@/components/chatUIComponent';
import { ChatAPI } from '../api';
import '../styles/global.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import {
  connectSocket,
  joinRoom,
  emitMessage,
  onMessage,
  disconnectSocket,
  setupHeartbeatAndReconnect,
  getSocketStatus,
} from '../utils/socketUtils';

import '../styles/chatui.css';
import defaultCustomStyles from '@/helper/customStyle';

import { getLocalStorageItem, setLocalStorageItem } from '@/utils/storage';
import config from '../helper/config.json';
import errorList from '../helper/errorList.json';

const ChatUI = forwardRef(
  (
    {
      customStyle,
      widgetCallBack,
      organizationId,
      agentId,
      discardCallBack,
      discardButtonText,
      callbackButtonText,
      selectedAgentWidget,
    },
    ref
  ) => {
    const ORGANIZATION_ID = organizationId
      ? organizationId
      : config.envVariables.ORGANIZATION_ID;
    const selectAgent = getLocalStorageItem('selectedAgent');

    const [messages, setMessages] = useState([
      {
        text: get(selectAgent, 'agentId.description', selectedAgentWidget),
        type: 'received',
        id: '',
      },
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const [error, setError] = useState(null);
    const [messageHistory, setMessageHistory] = useState([]);
    const [agents, setAgents] = useState([]);
    const [isInputDisabled, setIsInputDisabled] = useState(false);
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [selectedAgent, setSelectedAgent] = useState({});
    const [childId, setChildId] = useState('');
    const [roomId, setRoomId] = useState('');
    const [pendingMessages, setPendingMessages] = useState(new Map()); // Track pending messages
    const [connectionStatus, setConnectionStatus] = useState('connecting');
    const [responseTimeout, setResponseTimeout] = useState(null); // Track response timeout
    const customStyles = customStyle ? customStyle : defaultCustomStyles;

    const handleMessage = (message) => {
      console.log('=== MESSAGE RECEIVED ===');
      console.log('Raw message:', JSON.stringify(message, null, 2));

      // Clear response timeout since we received a message
      if (responseTimeout) {
        console.log('Clearing response timeout');
        clearTimeout(responseTimeout);
        setResponseTimeout(null);
      }

      // Clear any pending message that matches this response
      if (message._id || message.rootJobId) {
        const messageId = message._id || message.rootJobId;
        console.log('Clearing pending message:', messageId);
        setPendingMessages(prev => {
          const newMap = new Map(prev);
          newMap.delete(messageId);
          return newMap;
        });
      }

      try {
        setChildId(message._id);
        console.log('Message status:', message.status);

        const messageData =
          message.status === 'awaitingInput'
            ? message.inputs
            : message.successResp?.outputs;

        console.log('Message data extracted:', messageData);

        if (!messageData) {
          console.error('No message data found in response');
          setError('Invalid response format received');
          setIsTyping(false);
          return;
        }

        const updatedDataArray = messageData.map(
          ({ _id, validation, label, ...rest }) => rest
        );
        const updatedData = updatedDataArray.map((item) => ({
          ...item,
          metaData: 'string',
        }));

        console.log('Processed message data:', updatedData);
        setMessageHistory(updatedData);

        // Store messageHistory in localStorage for persistence across page reloads
        if (updatedData.length > 0) {
          setLocalStorageItem(`messageHistory_${message._id || roomId}`, updatedData);
          console.log('Stored messageHistory in localStorage for room:', message._id || roomId);
        }

        setIsTyping(false);

        let data = {};
        if (updatedData.length > 2) {
          data = { text: updatedData[1].data, type: 'received' };
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
          console.log('Multi-part message, continuing conversation');
        } else if (updatedData.length === 2) {
          data = {
            text: updatedData[1].data,
            type: 'received',
            isCompleted: true,
          };
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
          console.log('Conversation completed');
        } else {
          console.warn('Unexpected message data length:', updatedData.length);
          data = { text: 'Response received', type: 'received' };
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
        }

        console.log('Adding message to UI:', data);
        setMessages((prevMessages) => [...prevMessages, data]);
        setError(null); // Clear any previous errors
        setConnectionStatus('connected');
        console.log('=== MESSAGE PROCESSING COMPLETE ===');
      } catch (error) {
        console.error('Error processing message:', error);
        setError('Error processing response');
        setIsTyping(false);
      }
    };



    // Function to retry pending messages (will be defined after initializeSocket)
    const retryPendingMessages = useCallback(() => {
      if (pendingMessages.size === 0) return;

      console.log(`Retrying ${pendingMessages.size} pending messages...`);

      pendingMessages.forEach((messageData, messageId) => {
        const { event, message, roomId: msgRoomId, callback } = messageData;

        emitMessage(event, message, msgRoomId, null, (acknowledgment) => {
          if (acknowledgment === 'Message received successfully') {
            // Remove from pending messages on success
            setPendingMessages(prev => {
              const newMap = new Map(prev);
              newMap.delete(messageId);
              return newMap;
            });

            if (callback) {
              callback(acknowledgment);
            }
          } else {
            console.error(`Failed to retry message ${messageId}:`, acknowledgment);
          }
        });
      });
    }, [pendingMessages]);

    // Function to get history messages - defined before initializeSocket


    const getHistoryMessages = useCallback(async (id) => {
      console.log('=== GETTING HISTORY MESSAGES ===');
      console.log('Chat ID:', id);
      console.log('Organization ID:', ORGANIZATION_ID);

      try {
        // First, try to load messageHistory from localStorage
        const storedMessageHistory = getLocalStorageItem(`messageHistory_${id}`);
        console.log('Stored messageHistory from localStorage:', storedMessageHistory);

        if (storedMessageHistory && Array.isArray(storedMessageHistory) && storedMessageHistory.length > 0) {
          console.log('Found stored messageHistory, using it:', storedMessageHistory);
          setMessageHistory(storedMessageHistory);
        }

        // Get UI messages for display
        const resp = await ChatAPI.getChat(ORGANIZATION_ID, id);
        console.log('History API response:', resp);

        const messageResp = get(resp, 'data', []);
        console.log('Message response data:', messageResp);

        const filteredData = messageResp.filter((item) =>
          item.hasOwnProperty('text')
        );
        console.log('Filtered messages:', filteredData);

        if (filteredData.length > 0) {
          setChildId(filteredData[filteredData.length - 1].id);
          setMessages(prevMessages => [prevMessages[0], ...filteredData]);
          console.log('History messages loaded successfully:', filteredData.length);
        }

        // If no stored messageHistory was found, initialize a basic one
        if (!storedMessageHistory || storedMessageHistory.length === 0) {
          console.log('No stored messageHistory found, initializing basic structure');
          const basicMessageHistory = [
            {
              name: 'userInput',
              type: 'longText',
              data: '',
              metaData: 'string',
            }
          ];
          setMessageHistory(basicMessageHistory);
          console.log('Initialized basic messageHistory for conversation');
        }
      } catch (error) {
        console.error('Failed to get history messages:', error);
        setError('Failed to load chat history');

        // Initialize basic messageHistory even on error
        const basicMessageHistory = [
          {
            name: 'userInput',
            type: 'longText',
            data: '',
            metaData: 'string',
          }
        ];
        setMessageHistory(basicMessageHistory);
        console.log('Initialized basic messageHistory after error');
      }
    }, [ORGANIZATION_ID, setChildId, setMessages, setError, setMessageHistory]);

    // Memoized socket initialization function with improved error handling
    const initializeSocket = useCallback(
      (id, disconnected = false) => {
        console.log(`=== SOCKET INITIALIZATION START ===`);
        console.log(`Room ID: ${id}`);
        console.log(`Disconnected: ${disconnected}`);
        console.log(`API Base URL: ${config.envVariables.API_BASE_URL}`);

        try {
          // Connect to socket
          console.log('Connecting to socket...');
          const socket = connectSocket(config.envVariables.API_BASE_URL, disconnected);
          console.log('Socket connection initiated:', !!socket);

          setConnectionStatus('connecting');

          const { handleHeartbeatResponse, cleanup } =
            setupHeartbeatAndReconnect(setError, widgetCallBack);

          const handleError = (errorMessage) => {
            console.error('Socket error received:', errorMessage);
            setError(errorMessage);
            setConnectionStatus('error');

            // Implement retry logic for failed messages
            if (errorMessage === 'Failed to send message' || errorMessage.includes('socket not connected')) {
              console.log('Scheduling retry for failed message...');
              setTimeout(() => {
                console.log('Retrying failed message...');
                // Force reconnect and retry
                connectSocket(config.envVariables.API_BASE_URL, true);
                if (id) {
                  joinRoom(id);
                }
                retryPendingMessages();
              }, 2000);
            }
          };

          const handleConnect = () => {
            console.log('Socket connected successfully');
            setConnectionStatus('connected');
            setError(null);

            // Retry any pending messages
            retryPendingMessages();
          };

          const handleReconnect = () => {
            console.log('Socket reconnected');
            setConnectionStatus('connected');
            setError(null);

            // Rejoin room and retry pending messages
            if (id) {
              console.log('Rejoining room after reconnect:', id);
              joinRoom(id);
              setTimeout(() => retryPendingMessages(), 500);
            }
          };

          // Attach socket listeners
          console.log('Attaching socket event listeners...');
          onMessage('message', handleMessage);
          onMessage('error', handleError);
          onMessage('heartbeat', handleHeartbeatResponse);
          onMessage('connect', handleConnect);
          onMessage('reconnect', handleReconnect);
          console.log('Socket event listeners attached');

          // Join room and get messages with proper sequencing
          console.log('Joining room:', id);

          // Ensure socket is fully connected before joining room
          const socketStatus = getSocketStatus();
          if (socketStatus.connected) {
            joinRoom(id);

            // Wait a moment for room join to complete before getting history
            setTimeout(() => {
              if (!disconnected) {
                console.log('Getting history messages for room:', id);
                getHistoryMessages(id);
              } else {
                console.log('Skipping history messages (disconnected=true)');
              }
            }, 500);
          } else {
            // If not connected yet, wait for connection
            const waitForConnection = () => {
              const status = getSocketStatus();
              if (status.connected) {
                console.log('Socket connected, now joining room:', id);
                joinRoom(id);

                setTimeout(() => {
                  if (!disconnected) {
                    console.log('Getting history messages for room:', id);
                    getHistoryMessages(id);
                  }
                }, 500);
              } else {
                console.log('Still waiting for socket connection...');
                setTimeout(waitForConnection, 500);
              }
            };
            waitForConnection();
          }

          setConnectionStatus('connected');
          console.log('=== SOCKET INITIALIZATION COMPLETE ===');

          // Return cleanup function
          return () => {
            console.log('Cleaning up socket connection for room:', id);
            cleanup();
            disconnectSocket();
          };
        } catch (error) {
          console.error('Failed to initialize socket:', error);
          setError('Failed to establish connection');
          setConnectionStatus('error');
          throw error;
        }
      },
      [setError, widgetCallBack, retryPendingMessages, handleMessage, getHistoryMessages]
    );

    // Main initialization effect - runs once on mount
    useEffect(() => {
      console.log('=== COMPONENT MOUNTING ===');
      console.log('Current URL:', window.location.href);
      console.log('Widget mode:', config.envVariables.WIDGET);
      console.log('API Base URL:', config.envVariables.API_BASE_URL);

      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlChatId = urlParams.get('id');
        const urlAgentId = urlParams.get('agentID');

        console.log('URL parameters - chatId:', urlChatId, 'agentId:', urlAgentId);

        if (urlChatId) {
          console.log('Setting roomId from URL:', urlChatId);
          // Add a small delay to ensure component is fully mounted
          setTimeout(() => {
            setRoomId(urlChatId);
          }, 100);
        } else {
          console.log('No chat ID in URL, this is a new conversation');
          // Initialize basic messageHistory for new conversations
          const basicMessageHistory = [
            {
              name: 'userInput',
              type: 'longText',
              data: '',
              metaData: 'string',
            }
          ];
          setMessageHistory(basicMessageHistory);
          console.log('Initialized messageHistory for new conversation');
        }
      }
    }, []); // Only run once on mount

    // Socket initialization effect - runs when roomId changes
    useEffect(() => {
      if (!roomId) {
        console.log('No roomId, skipping socket initialization');
        return;
      }

      console.log('=== INITIALIZING SOCKET ===');
      console.log('Room ID:', roomId);

      let cleanup;
      let retryCount = 0;
      const maxRetries = 3;

      const attemptConnection = () => {
        try {
          cleanup = initializeSocket(roomId);
          console.log('Socket initialization completed for room:', roomId);

          // Verify connection after a short delay
          setTimeout(() => {
            const status = getSocketStatus();
            console.log('Socket status after initialization:', status);

            if (!status.connected && retryCount < maxRetries) {
              retryCount++;
              console.log(`Socket not connected, retry attempt ${retryCount}/${maxRetries}`);
              setTimeout(attemptConnection, 1000 * retryCount); // Exponential backoff
            } else if (!status.connected) {
              console.error('Failed to establish socket connection after retries');
              setError('Connection failed. Please refresh the page.');
            }
          }, 1000);

        } catch (error) {
          console.error('Socket initialization failed:', error);
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`Retrying socket initialization ${retryCount}/${maxRetries}`);
            setTimeout(attemptConnection, 1000 * retryCount);
          } else {
            setError('Failed to establish connection. Please refresh the page.');
          }
        }
      };

      // Start connection attempt with a small delay
      const timer = setTimeout(attemptConnection, 300);

      return () => {
        clearTimeout(timer);
        if (cleanup) {
          cleanup();
        }
      };
    }, [roomId]);

    // Handle page visibility changes and connection recovery
    useEffect(() => {
      const handleVisibilityChange = () => {
        if (!document.hidden && roomId) {
          // Page became visible, check connection status
          const status = getSocketStatus();
          console.log('Page visible, socket status:', status);

          if (!status.connected) {
            console.log('Reconnecting socket after page became visible...');
            setTimeout(() => {
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(roomId);
              getHistoryMessages(roomId);
            }, 1000);
          }
        }
      };

      const handleOnline = () => {
        console.log('Network connection restored');
        if (roomId) {
          setTimeout(() => {
            connectSocket(config.envVariables.API_BASE_URL, true);
            joinRoom(roomId);
            getHistoryMessages(roomId);
          }, 1000);
        }
      };

      const handleOffline = () => {
        console.log('Network connection lost');
        setConnectionStatus('offline');
        setError('Network connection lost. Will reconnect automatically.');
      };

      // Add event listeners
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Cleanup
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }, [roomId]);

    // Periodic connection health check
    useEffect(() => {
      if (!roomId) return;

      const healthCheckInterval = setInterval(() => {
        const status = getSocketStatus();

        if (!status.connected && connectionStatus !== 'offline') {
          console.log('Health check: Socket disconnected, attempting reconnection...');
          setConnectionStatus('reconnecting');
          connectSocket(config.envVariables.API_BASE_URL, true);
          joinRoom(roomId);
        }
      }, 30000); // Check every 30 seconds

      return () => clearInterval(healthCheckInterval);
    }, [roomId, connectionStatus]);

    // Initialize agents and selected agent on mount
    useEffect(() => {
      console.log('=== LOADING AGENTS AND SELECTED AGENT ===');

      const agent = getLocalStorageItem('agents');
      const selectedAgent = getLocalStorageItem('selectedAgent');

      console.log('Agents from localStorage:', agent?.length || 0);
      console.log('Selected agent from localStorage:', selectedAgent?.agentId?._id);

      if (agent) {
        setAgents(agent);
      }

      // Check URL for agent selection on page load
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlAgentId = urlParams.get('agentID');

        console.log('Agent ID from URL:', urlAgentId);

        if (urlAgentId && agent) {
          // Find the agent from the list that matches the URL agent ID
          const agentFromUrl = agent.find(a => get(a, 'agentId._id') === urlAgentId);
          if (agentFromUrl) {
            console.log('Setting agent from URL:', agentFromUrl.agentId._id);
            setSelectedAgent(agentFromUrl);
            setLocalStorageItem('selectedAgent', agentFromUrl);
          } else {
            console.log('Agent not found in list for ID:', urlAgentId);
            if (selectedAgent) {
              setSelectedAgent(selectedAgent);
            }
          }
        } else if (selectedAgent) {
          console.log('Using selected agent from localStorage');
          setSelectedAgent(selectedAgent);
        }
      } else if (selectedAgent) {
        setSelectedAgent(selectedAgent);
      }
    }, []);



    const handleSelectAgent = (agent) => {
      setLocalStorageItem('selectedAgent', agent);
      setSelectedAgent(agent);

      if (typeof window !== 'undefined') {
        const url = new URL(window.location);

        // Clear any existing chat session ID since we're selecting a new agent
        url.searchParams.delete('id');
        // Set the agent ID parameter
        url.searchParams.set('agentID', agent.agentId._id);

        // Update the URL without reloading the page
        window.history.replaceState(window.history.state, '', url);

        // Reset messages for new agent
        setMessages([
          {
            text: get(
              agent,
              'agentId.description',
              'Welcome! how can i assist you ?'
            ),
            type: 'received',
            id: '',
          },
        ]);
        setIsTyping(false);
        setIsInputDisabled(false);
        setIsButtonDisabled(false);

        // Clear room ID to start fresh conversation
        setRoomId('');
      }
    };

    if (config.envVariables.WIDGET === 'no') {
      useImperativeHandle(ref, () => ({
        handleSelectAgent,
      }));
    }
    useImperativeHandle(ref, () => ({
      sendMessage,
      setInputMessage,
    }));
    const fetchData = async (inputMessage = 'Write my Profile for chef') => {
      console.log('=== STARTING NEW CONVERSATION ===');
      console.log('Input message:', inputMessage);

      setMessages([...messages, { text: inputMessage, type: 'sent' }]);
      let selectedAgentId = '';
      setIsTyping(true);
      setIsInputDisabled(true);
      setIsButtonDisabled(true);

      const isWidget = config.envVariables.WIDGET;
      const selectedAgent = getLocalStorageItem('selectedAgent');

      console.log('Widget mode:', isWidget);
      console.log('Selected agent from localStorage:', selectedAgent?.agentId?._id);

      // Check URL for agentID parameter first (for non-widget mode)
      if (isWidget === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlAgentId = urlParams.get('agentID');
        console.log('Agent ID from URL:', urlAgentId);

        if (urlAgentId) {
          selectedAgentId = urlAgentId;
          console.log('Using agent ID from URL:', selectedAgentId);
        } else if (get(selectedAgent, 'agentId._id', '') !== '') {
          selectedAgentId = get(selectedAgent, 'agentId._id', '');
          console.log('Using agent ID from localStorage:', selectedAgentId);
        } else {
          selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
          console.log('Using fallback agent ID:', selectedAgentId);
        }
      } else if (isWidget === 'yes' || get(selectedAgent, 'agentId._id', '') === '') {
        selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
        console.log('Using widget/fallback agent ID:', selectedAgentId);
      } else {
        selectedAgentId = get(selectedAgent, 'agentId._id', '');
        console.log('Using selected agent ID:', selectedAgentId);
      }

      console.log('Final selected agent ID:', selectedAgentId);
      const data = {
        inputs: [
          {
            name: 'userInput',
            type: 'longText',
            data: inputMessage,
            metaData: 'string',
          },
        ],
        agentId: selectedAgentId,
        organizationId: organizationId
          ? organizationId
          : config.envVariables.ORGANIZATION_ID,
        metaData: {
          name: 'sachin',
        },
        callbackUrl: config.envVariables.CALL_BACK_URL,
      };
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      try {
        if (!id) {
          console.log('Making API call to start chat...');
          console.log('Chat data:', JSON.stringify(data, null, 2));

          const response = await ChatAPI.startChat(data);
          console.log('Start chat API response:', response);
          console.log('Response status code:', response.statusCode);
          console.log('Response data:', response.data);

          if (response.statusCode === 200) {
            const id = response.data._id;
            console.log('Chat started successfully with ID:', id);

            setRoomId(id);
            setChildId(id);

            if (
              typeof window !== 'undefined' &&
              config.envVariables.WIDGET === 'no'
            ) {
              const url = new URL(window.location);
              // Set the chat session ID
              url.searchParams.set('id', id);
              // Preserve the agent ID if it exists
              const currentAgentId = url.searchParams.get('agentID');
              if (currentAgentId) {
                url.searchParams.set('agentID', currentAgentId);
              }
              console.log('Updated URL:', url.toString());
              window.history.replaceState(window.history.state, '', url);
            }

            console.log('Connecting socket and joining room...');
            connectSocket(config.envVariables.API_BASE_URL);
            joinRoom(id);

            // Set up socket event listeners for this new chat
            console.log('Setting up socket listeners for new chat...');
            onMessage('message', handleMessage);

          } else {
            console.error('Start chat failed with status:', response.statusCode);
            console.error('Error response:', response);
            setIsTyping(false);
            setIsInputDisabled(false);
            setIsButtonDisabled(false);
            setError(`Failed to start chat (${response.statusCode})`);
            if (widgetCallBack) {
              widgetCallBack(errorList.serverNotResponding, null);
            }
          }
        }
      } catch (error) {
        console.error('Start chat API error:', error);
        console.error('Error details:', error.response?.data || error.message);
        setIsTyping(false);
        setIsInputDisabled(false);
        setIsButtonDisabled(false);
        setError(`Network error: ${error.message}`);
      }
    };

    const sendMessage = async () => {
      console.log('=== SENDING MESSAGE ===');
      console.log('Input message:', inputMessage);
      console.log('Current roomId state:', roomId);

      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        console.log('Room ID from URL:', id);
        setRoomId(id);
      } else {
        id = roomId;
        console.log('Using roomId from state:', id);
      }

      console.log('Final room ID for sending:', id);

      if (inputMessage.trim() !== '') {
        if (!id) {
          console.log('No room ID, starting new chat...');
          await fetchData(inputMessage);
        } else {
          console.log('Existing chat, sending message to room:', id);
          console.log('Current messageHistory:', messageHistory);

          // Ensure messageHistory has the correct structure
          let updatedMessageHistory = [...messageHistory];

          const index = updatedMessageHistory.findIndex(
            (item) => item.name === 'userInput'
          );

          if (index !== -1) {
            // Update existing userInput
            updatedMessageHistory[index].data = inputMessage;
            console.log('Updated existing userInput in messageHistory');
          } else {
            // Create new userInput if not found (this happens after page reload)
            console.log('No userInput found in messageHistory, creating new one');
            updatedMessageHistory = [
              {
                name: 'userInput',
                type: 'longText',
                data: inputMessage,
                metaData: 'string',
              },
              ...updatedMessageHistory
            ];
          }

          console.log('Final messageHistory for sending:', updatedMessageHistory);

          const data = {
            inputs: updatedMessageHistory,
          };

          // Update the messageHistory state with the corrected data
          setMessageHistory(updatedMessageHistory);

          // Store updated messageHistory in localStorage
          setLocalStorageItem(`messageHistory_${id}`, updatedMessageHistory);
          console.log('Stored updated messageHistory in localStorage for room:', id);

          const messageId = `${Date.now()}_${Math.random()}`;
          const messageData = {
            event: 'message',
            message: { data, childId },
            roomId: id,
            callback: (acknowledgment) => {
              if (acknowledgment === 'Message received successfully') {
                console.log('Message sent successfully');
                // Remove from pending messages
                setPendingMessages(prev => {
                  const newMap = new Map(prev);
                  newMap.delete(messageId);
                  return newMap;
                });
              } else {
                console.error('Message failed:', acknowledgment);
                setError('Failed to send message. Will retry automatically.');
              }
            }
          };

          // Check socket status before sending
          const socketStatus = getSocketStatus();
          console.log('Socket status before sending:', socketStatus);

          // Add to pending messages before sending
          setPendingMessages(prev => new Map(prev.set(messageId, messageData)));

          console.log('Emitting message to room:', id);
          console.log('Message data:', { data, childId });

          emitMessage(
            'message',
            { data, childId },
            id,
            null, // Remove circular reference
            messageData.callback
          );

          console.log('Message emitted successfully');

          // Set up response timeout
          const timeout = setTimeout(() => {
            console.log('Agent response timeout detected');
            setError('Agent is taking longer than expected. Checking connection...');

            // Check socket status and attempt reconnection if needed
            const status = getSocketStatus();
            if (!status.connected) {
              console.log('Socket disconnected during timeout, reconnecting...');
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(id);
            } else {
              // Socket is connected but no response, this might be the agent taking time
              console.log('Socket connected but no agent response, continuing to wait...');
              setError('Agent is processing your request. This may take a moment...');
            }
          }, config.timeouts.AGENT_RESPONSE_TIMEOUT || 120000); // 2 minutes default

          setResponseTimeout(timeout);

          setIsTyping(true);
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
          setError(null);
          setMessages([
            ...messages,
            { text: inputMessage, type: 'sent', id: '' },
          ]);
          setInputMessage('');
        }
      }
    };



    const handleInputChange = (event) => {
      setInputMessage(event.target.value);
    };

    function smoothScrollToBottom(element) {
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;
      const maxScrollTop = scrollHeight - clientHeight;
      const currentScrollTop = element.scrollTop;
      const targetScrollTop = Math.min(
        maxScrollTop,
        currentScrollTop + clientHeight
      );
      const distance = targetScrollTop - currentScrollTop;
      const duration = 300; // Adjust the duration as needed

      let start = null;
      function step(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const increment = (distance * progress) / duration;
        element.scrollTo(0, currentScrollTop + increment);
        if (progress < duration) {
          requestAnimationFrame(step);
        }
      }
      requestAnimationFrame(step);
    }

    return (
      <ChatUIComponent
        agents={agents}
        messages={messages}
        handleInputChange={handleInputChange}
        sendMessage={sendMessage}
        customStyles={customStyles}
        smoothScrollToBottom={smoothScrollToBottom}
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        isTyping={isTyping}
        isInputDisabled={isInputDisabled}
        selectedAgent={selectedAgent}
        handleSelectAgent={handleSelectAgent}
        widgetCallBack={widgetCallBack}
        discardCallBack={discardCallBack}
        discardButtonText={discardButtonText}
        callbackButtonText={callbackButtonText}
        isButtonDisabled={isButtonDisabled}
      />
    );
  }
);

export default ChatUI;
