'use client';
import React from 'react';
import {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useCallback,
} from 'react';
import { get } from 'lodash';
import ChatUIComponent from '@/components/chatUIComponent';
import { ChatAPI } from '../api';
import '../styles/global.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import {
  connectSocket,
  joinRoom,
  emitMessage,
  onMessage,
  disconnectSocket,
  setupHeartbeatAndReconnect,
  getSocketStatus,
} from '../utils/socketUtils';

import '../styles/chatui.css';
import defaultCustomStyles from '@/helper/customStyle';

import { getLocalStorageItem, setLocalStorageItem } from '@/utils/storage';
import config from '../helper/config.json';
import errorList from '../helper/errorList.json';

const ChatUI = forwardRef(
  (
    {
      customStyle,
      widgetCallBack,
      organizationId,
      agentId,
      discardCallBack,
      discardButtonText,
      callbackButtonText,
      selectedAgentWidget,
    },
    ref
  ) => {
    const ORGANIZATION_ID = organizationId
      ? organizationId
      : config.envVariables.ORGANIZATION_ID;
    const selectAgent = getLocalStorageItem('selectedAgent');

    const [messages, setMessages] = useState([
      {
        text: get(selectAgent, 'agentId.description', selectedAgentWidget),
        type: 'received',
        id: '',
      },
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const [error, setError] = useState(null);
    const [messageHistory, setMessageHistory] = useState([]);
    const [agents, setAgents] = useState([]);
    const [isInputDisabled, setIsInputDisabled] = useState(false);
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [selectedAgent, setSelectedAgent] = useState({});
    const [childId, setChildId] = useState('');
    const [roomId, setRoomId] = useState('');
    const [pendingMessages, setPendingMessages] = useState(new Map()); // Track pending messages
    const [connectionStatus, setConnectionStatus] = useState('connecting');
    const [responseTimeout, setResponseTimeout] = useState(null); // Track response timeout
    const customStyles = customStyle ? customStyle : defaultCustomStyles;

    const handleMessage = (message) => {
      console.log('Received message:', message);

      // Clear response timeout since we received a message
      if (responseTimeout) {
        clearTimeout(responseTimeout);
        setResponseTimeout(null);
      }

      // Clear any pending message that matches this response
      if (message._id || message.rootJobId) {
        const messageId = message._id || message.rootJobId;
        setPendingMessages(prev => {
          const newMap = new Map(prev);
          newMap.delete(messageId);
          return newMap;
        });
      }

      setChildId(message._id);
      const messageData =
        message.status === 'awaitingInput'
          ? message.inputs
          : message.successResp.outputs;
      const updatedDataArray = messageData.map(
        ({ _id, validation, label, ...rest }) => rest
      );
      const updatedData = updatedDataArray.map((item) => ({
        ...item,
        metaData: 'string',
      }));
      setMessageHistory(updatedData);
      setIsTyping(false);

      let data = {};
      if (updatedData.length > 2) {
        data = { text: updatedData[1].data, type: 'received' };
        setIsInputDisabled(false);
        setIsButtonDisabled(false);
      } else if (updatedData.length === 2) {
        data = {
          text: updatedData[1].data,
          type: 'received',
          isCompleted: true,
        };
        setIsInputDisabled(true);
        setIsButtonDisabled(true);
      }

      setMessages((prevMessages) => [...prevMessages, data]);
      setError(null); // Clear any previous errors
      setConnectionStatus('connected');
    };

    const getRoomId = useCallback(
      (currentRoomId) => {
        if (config.envVariables.WIDGET === 'no') {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get('id');
        }
        return currentRoomId;
      },
      [] // No dependencies, as it only relies on constants
    );

    // Function to retry pending messages (will be defined after initializeSocket)
    const retryPendingMessages = useCallback(() => {
      if (pendingMessages.size === 0) return;

      console.log(`Retrying ${pendingMessages.size} pending messages...`);

      pendingMessages.forEach((messageData, messageId) => {
        const { event, message, roomId: msgRoomId, callback } = messageData;

        emitMessage(event, message, msgRoomId, null, (acknowledgment) => {
          if (acknowledgment === 'Message received successfully') {
            // Remove from pending messages on success
            setPendingMessages(prev => {
              const newMap = new Map(prev);
              newMap.delete(messageId);
              return newMap;
            });

            if (callback) {
              callback(acknowledgment);
            }
          } else {
            console.error(`Failed to retry message ${messageId}:`, acknowledgment);
          }
        });
      });
    }, [pendingMessages]);

    // Memoized socket initialization function with improved error handling
    const initializeSocket = useCallback(
      (id, disconnected = false) => {
        console.log(`Initializing socket for room: ${id}, disconnected: ${disconnected}`);

        try {
          connectSocket(config.envVariables.API_BASE_URL, disconnected);
          setConnectionStatus('connecting');

          const { handleHeartbeatResponse, cleanup } =
            setupHeartbeatAndReconnect(setError, widgetCallBack);

          const handleError = (errorMessage) => {
            console.error('Socket error:', errorMessage);
            setError(errorMessage);
            setConnectionStatus('error');

            // Implement retry logic for failed messages
            if (errorMessage === 'Failed to send message' || errorMessage.includes('socket not connected')) {
              setTimeout(() => {
                console.log('Retrying failed message...');
                // Force reconnect and retry
                connectSocket(config.envVariables.API_BASE_URL, true);
                if (id) {
                  joinRoom(id);
                }
                retryPendingMessages();
              }, 2000);
            }
          };

          const handleConnect = () => {
            console.log('Socket connected successfully');
            setConnectionStatus('connected');
            setError(null);

            // Retry any pending messages
            retryPendingMessages();
          };

          const handleReconnect = () => {
            console.log('Socket reconnected');
            setConnectionStatus('connected');
            setError(null);

            // Rejoin room and retry pending messages
            if (id) {
              joinRoom(id);
              setTimeout(() => retryPendingMessages(), 500);
            }
          };

          // Attach socket listeners
          onMessage('message', handleMessage);
          onMessage('error', handleError);
          onMessage('heartbeat', handleHeartbeatResponse);
          onMessage('connect', handleConnect);
          onMessage('reconnect', handleReconnect);

          // Join room and get messages
          joinRoom(id);
          if (!disconnected) {
            getHistoryMessages(id);
          }

          setConnectionStatus('connected');

          // Return cleanup function
          return () => {
            cleanup();
            disconnectSocket();
          };
        } catch (error) {
          console.error('Failed to initialize socket:', error);
          setError('Failed to establish connection');
          setConnectionStatus('error');
          throw error;
        }
      },
      [setError, widgetCallBack] // Dependencies to prevent unnecessary re-creation
    );

    useEffect(() => {
      const id = getRoomId(roomId); // Get room ID
      setRoomId(id);

      if (id) {
        const cleanup = initializeSocket(id); // Set up socket and listeners
        return cleanup; // Cleanup on unmount or dependency change
      }
    }, [roomId, getRoomId]);

    // Handle page visibility changes and connection recovery
    useEffect(() => {
      const handleVisibilityChange = () => {
        if (!document.hidden && roomId) {
          // Page became visible, check connection status
          const status = getSocketStatus();
          console.log('Page visible, socket status:', status);

          if (!status.connected) {
            console.log('Reconnecting socket after page became visible...');
            setTimeout(() => {
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(roomId);
              getHistoryMessages(roomId);
            }, 1000);
          }
        }
      };

      const handleOnline = () => {
        console.log('Network connection restored');
        if (roomId) {
          setTimeout(() => {
            connectSocket(config.envVariables.API_BASE_URL, true);
            joinRoom(roomId);
            getHistoryMessages(roomId);
          }, 1000);
        }
      };

      const handleOffline = () => {
        console.log('Network connection lost');
        setConnectionStatus('offline');
        setError('Network connection lost. Will reconnect automatically.');
      };

      // Add event listeners
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Cleanup
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }, [roomId]);

    // Periodic connection health check
    useEffect(() => {
      if (!roomId) return;

      const healthCheckInterval = setInterval(() => {
        const status = getSocketStatus();

        if (!status.connected && connectionStatus !== 'offline') {
          console.log('Health check: Socket disconnected, attempting reconnection...');
          setConnectionStatus('reconnecting');
          connectSocket(config.envVariables.API_BASE_URL, true);
          joinRoom(roomId);
        }
      }, 30000); // Check every 30 seconds

      return () => clearInterval(healthCheckInterval);
    }, [roomId, connectionStatus]);

    useEffect(() => {
      const agent = getLocalStorageItem('agents');
      const selectedAgent = getLocalStorageItem('selectedAgent');
      if (selectedAgent) {
        setSelectedAgent(selectedAgent);
      }
      if (agent) {
        setAgents(agent);
      }
    }, []);

    const getHistoryMessages = async (id) => {
      const resp = await ChatAPI.getChat(ORGANIZATION_ID, id);
      const messageResp = get(resp, 'data', []);
      const filteredData = messageResp.filter((item) =>
        item.hasOwnProperty('text')
      );
      setChildId(filteredData[filteredData.length - 1].id);
      setMessages([messages[0], ...filteredData]);
    };

    const handleSelectAgent = (agent) => {
      setLocalStorageItem('selectedAgent', agent);
      setSelectedAgent(agent);

      if (typeof window !== 'undefined') {
        const { pathname } = window.location;
        const url = new URL(window.location);

        // Set the new parameter
        url.searchParams.set('id', agent.agentId._id);

        // Update the URL without reloading the page
        window.history.replaceState(window.history.state, '', url);

        // Use pushState to navigate to the new URL without reloading
        if (pathname && pathname !== '') {
          setMessages([
            {
              text: get(
                agent,
                'agentId.description',
                'Welcome! how can i assist you ?'
              ),
              type: 'received',
              id: '',
            },
          ]);
          setIsTyping(false);
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
          window.history.pushState(
            {},
            '',
            `${pathname}?agentID=${agent.agentId._id}`
          );
        } else {
          setMessages([
            {
              text: get(
                agent,
                'agentId.description',
                'Welcome! how can i assist you ?'
              ),
              type: 'received',
              id: '',
            },
          ]);
          setIsTyping(false);
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
          window.history.pushState({}, '', `/?agentID=${agent.agentId._id}`);
        }
      }
    };

    if (config.envVariables.WIDGET === 'no') {
      useImperativeHandle(ref, () => ({
        handleSelectAgent,
      }));
    }
    useImperativeHandle(ref, () => ({
      sendMessage,
      setInputMessage,
    }));
    const fetchData = async (inputMessage = 'Write my Profile for chef') => {
      setMessages([...messages, { text: inputMessage, type: 'sent' }]);
      let selectedAgentId = '';
      setIsTyping(true);
      setIsInputDisabled(true);
      setIsButtonDisabled(true);
      const isWidget = config.envVariables.WIDGET;
      const selectedAgent = getLocalStorageItem('selectedAgent');
      if (isWidget === 'yes' || get(selectedAgent, 'agentId._id', '') === '') {
        selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
      } else {
        selectedAgentId = get(selectedAgent, 'agentId._id', '');
      }
      const data = {
        inputs: [
          {
            name: 'userInput',
            type: 'longText',
            data: inputMessage,
            metaData: 'string',
          },
        ],
        agentId: selectedAgentId,
        organizationId: organizationId
          ? organizationId
          : config.envVariables.ORGANIZATION_ID,
        metaData: {
          name: 'sachin',
        },
        callbackUrl: config.envVariables.CALL_BACK_URL,
      };
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      try {
        if (!id) {
          const response = await ChatAPI.startChat(data);
          if (response.statusCode === 200) {
            const id = response.data._id;
            setRoomId(id);
            if (
              typeof window !== 'undefined' &&
              config.envVariables.WIDGET === 'no'
            ) {
              const url = new URL(window.location);
              url.searchParams.set('id', id);
              window.history.replaceState(window.history.state, '', url);
              // window.location.href = `/${response.data._id}`;
            }
            connectSocket(config.envVariables.API_BASE_URL);
            joinRoom(id);
          } else {
            errorList(errorList.serverNotResponding, null);
          }
        }
      } catch (error) {
        // setError(error);
      }
    };

    const sendMessage = async () => {
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      if (inputMessage.trim() !== '') {
        if (!id) {
          await fetchData(inputMessage);
        } else {
          const index = messageHistory.findIndex(
            (item) => item.name === 'userInput'
          );
          if (index !== -1) {
            messageHistory[index].data = inputMessage;
          }

          const data = {
            inputs: messageHistory,
          };

          const messageId = `${Date.now()}_${Math.random()}`;
          const messageData = {
            event: 'message',
            message: { data, childId },
            roomId: id,
            callback: (acknowledgment) => {
              if (acknowledgment === 'Message received successfully') {
                console.log('Message sent successfully');
                // Remove from pending messages
                setPendingMessages(prev => {
                  const newMap = new Map(prev);
                  newMap.delete(messageId);
                  return newMap;
                });
              } else {
                console.error('Message failed:', acknowledgment);
                setError('Failed to send message. Will retry automatically.');
              }
            }
          };

          // Add to pending messages before sending
          setPendingMessages(prev => new Map(prev.set(messageId, messageData)));

          emitMessage(
            'message',
            { data, childId },
            id,
            null, // Remove circular reference
            messageData.callback
          );

          // Set up response timeout
          const timeout = setTimeout(() => {
            console.log('Agent response timeout detected');
            setError('Agent is taking longer than expected. Checking connection...');

            // Check socket status and attempt reconnection if needed
            const status = getSocketStatus();
            if (!status.connected) {
              console.log('Socket disconnected during timeout, reconnecting...');
              connectSocket(config.envVariables.API_BASE_URL, true);
              joinRoom(id);
            } else {
              // Socket is connected but no response, this might be the agent taking time
              console.log('Socket connected but no agent response, continuing to wait...');
              setError('Agent is processing your request. This may take a moment...');
            }
          }, config.timeouts.AGENT_RESPONSE_TIMEOUT || 120000); // 2 minutes default

          setResponseTimeout(timeout);

          setIsTyping(true);
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
          setError(null);
          setMessages([
            ...messages,
            { text: inputMessage, type: 'sent', id: '' },
          ]);
          setInputMessage('');
        }
      }
    };



    const handleInputChange = (event) => {
      setInputMessage(event.target.value);
    };

    function smoothScrollToBottom(element) {
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;
      const maxScrollTop = scrollHeight - clientHeight;
      const currentScrollTop = element.scrollTop;
      const targetScrollTop = Math.min(
        maxScrollTop,
        currentScrollTop + clientHeight
      );
      const distance = targetScrollTop - currentScrollTop;
      const duration = 300; // Adjust the duration as needed

      let start = null;
      function step(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const increment = (distance * progress) / duration;
        element.scrollTo(0, currentScrollTop + increment);
        if (progress < duration) {
          requestAnimationFrame(step);
        }
      }
      requestAnimationFrame(step);
    }

    return (
      <ChatUIComponent
        agents={agents}
        messages={messages}
        handleInputChange={handleInputChange}
        sendMessage={sendMessage}
        customStyles={customStyles}
        smoothScrollToBottom={smoothScrollToBottom}
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        isTyping={isTyping}
        isInputDisabled={isInputDisabled}
        selectedAgent={selectedAgent}
        handleSelectAgent={handleSelectAgent}
        widgetCallBack={widgetCallBack}
        discardCallBack={discardCallBack}
        discardButtonText={discardButtonText}
        callbackButtonText={callbackButtonText}
        isButtonDisabled={isButtonDisabled}
      />
    );
  }
);

export default ChatUI;
