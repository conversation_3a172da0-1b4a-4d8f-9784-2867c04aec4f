import React, { useState, useEffect } from 'react';
import { getSocketStatus } from '../utils/socketUtils';

const DebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateDebugInfo = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const socketStatus = getSocketStatus();
      
      setDebugInfo({
        timestamp: new Date().toISOString(),
        url: window.location.href,
        urlParams: {
          id: urlParams.get('id'),
          agentID: urlParams.get('agentID')
        },
        socketStatus,
        localStorage: {
          selectedAgent: localStorage.getItem('selectedAgent'),
          agents: localStorage.getItem('agents')
        }
      });
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 2000);

    return () => clearInterval(interval);
  }, []);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const testConnection = () => {
    console.log('=== CONNECTION TEST ===');
    console.log('Current debug info:', debugInfo);
    
    // Test socket status
    const status = getSocketStatus();
    console.log('Socket status:', status);
    
    // Test URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    console.log('URL parameters:', {
      id: urlParams.get('id'),
      agentID: urlParams.get('agentID')
    });
    
    // Test localStorage
    console.log('LocalStorage:', {
      selectedAgent: localStorage.getItem('selectedAgent'),
      agents: localStorage.getItem('agents')
    });
  };

  const forceReconnect = () => {
    console.log('=== FORCING RECONNECT ===');
    const urlParams = new URLSearchParams(window.location.search);
    const chatId = urlParams.get('id');
    
    if (chatId) {
      // Import and use socket utilities
      import('../utils/socketUtils').then(({ connectSocket, joinRoom }) => {
        connectSocket('https://api.dev.daiquiri.activate.bar', true);
        joinRoom(chatId);
        console.log('Reconnection attempted for room:', chatId);
      });
    } else {
      console.log('No chat ID available for reconnection');
    }
  };

  if (!isVisible) {
    return (
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        zIndex: 9999,
        backgroundColor: '#007bff',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '5px',
        cursor: 'pointer',
        fontSize: '12px'
      }} onClick={toggleVisibility}>
        Debug
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '400px',
      maxHeight: '500px',
      backgroundColor: 'white',
      border: '1px solid #ccc',
      borderRadius: '5px',
      padding: '10px',
      fontSize: '12px',
      zIndex: 9999,
      overflow: 'auto',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h4 style={{ margin: 0 }}>Debug Panel</h4>
        <button onClick={toggleVisibility} style={{ background: 'none', border: 'none', fontSize: '16px', cursor: 'pointer' }}>×</button>
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <button onClick={testConnection} style={{ marginRight: '5px', padding: '5px 10px', fontSize: '11px' }}>
          Test Connection
        </button>
        <button onClick={forceReconnect} style={{ padding: '5px 10px', fontSize: '11px' }}>
          Force Reconnect
        </button>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>URL:</strong>
        <div style={{ wordBreak: 'break-all', backgroundColor: '#f5f5f5', padding: '5px', borderRadius: '3px' }}>
          {debugInfo.url}
        </div>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>URL Parameters:</strong>
        <pre style={{ backgroundColor: '#f5f5f5', padding: '5px', borderRadius: '3px', margin: '5px 0' }}>
          {JSON.stringify(debugInfo.urlParams, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Socket Status:</strong>
        <pre style={{ backgroundColor: '#f5f5f5', padding: '5px', borderRadius: '3px', margin: '5px 0' }}>
          {JSON.stringify(debugInfo.socketStatus, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Connection Status:</strong>
        <span style={{ 
          color: debugInfo.socketStatus?.connected ? 'green' : 'red',
          fontWeight: 'bold'
        }}>
          {debugInfo.socketStatus?.connected ? 'CONNECTED' : 'DISCONNECTED'}
        </span>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>LocalStorage:</strong>
        <div style={{ fontSize: '10px' }}>
          <div>Selected Agent: {debugInfo.localStorage?.selectedAgent ? 'Present' : 'Missing'}</div>
          <div>Agents List: {debugInfo.localStorage?.agents ? 'Present' : 'Missing'}</div>
        </div>
      </div>

      <div style={{ fontSize: '10px', color: '#666' }}>
        Last updated: {debugInfo.timestamp}
      </div>
    </div>
  );
};

export default DebugPanel;
