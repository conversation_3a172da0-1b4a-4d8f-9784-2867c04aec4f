#!/bin/bash

if [[ $1 = "" || $1 = "help" ]]; then
  echo "use: deploy [api|webapp]"
  echo ""
  echo "The scripts is aware of the branch:"
  echo "- 'develop' will deploy to *development* environment"
  echo "- 'production' will deploy to *production* environment"
  exit 1
fi

# Validate branch
BRANCH=$(git rev-parse --abbrev-ref HEAD)

if [[ $BRANCH = "develop" || $BRANCH = "production" ]]; then
  if [[ $BRANCH = "develop" ]]; then
    ENV=dev
  fi
  if [[ $BRANCH = "production" ]]; then
    ENV=prod
  fi
else
  echo "Deploy not allowed from '${BRANCH}' branch!"
  echo "Please switch to [develop|production] branch"
  exit 1
fi

# Validate input arguments
if [[ $1 = "api" || $1 = "webapp" ]]; then
  PACKAGE=$1
else
  echo "Unsupported package! ${1}"
  echo "Please choose [api|webapp]"
  exit 1
fi

SCRIPT=`realpath -s $0`
CWD=`dirname $SCRIPT`
AWS_PROFILE="533267084389"
AWS_REGION=eu-west-2
REPO_NAME="daiquiri/${PACKAGE}"
IMAGE_REPO_NAME="${ENV}/${REPO_NAME}"
IMAGE_TAG=$(git rev-parse --short HEAD)
ECR_REPO_URL=${AWS_PROFILE}.dkr.ecr.${AWS_REGION}.amazonaws.com/${IMAGE_REPO_NAME}

echo "**************************************"
echo "* REVIEW DETAILS BEFORE YOU PROCEED! *"
echo "**************************************"
echo "CWD: ${CWD}"
echo "BRANCH: ${BRANCH}"
echo "AWS_PROFILE: ${AWS_PROFILE}"
echo "AWS_REGION: ${AWS_REGION}"
echo "ENV: ${ENV}"
echo "PACKAGE: ${PACKAGE}"
echo "IMAGE_REPO_NAME: ${IMAGE_REPO_NAME}"
echo "IMAGE_TAG: ${IMAGE_TAG}"
echo "ECR_REPO_URL: ${ECR_REPO_URL}"
echo ""

while true; do
    read -p "Do you wish to continue? " yn
    case $yn in
        [Yy]* ) break;;
        [Nn]* ) exit;;
        * ) echo "Please answer yes or no.";;
    esac
done

echo Login to ECR
aws ecr get-login-password --profile ${AWS_PROFILE} --region ${AWS_REGION} | docker login --username AWS --password-stdin $ECR_REPO_URL

cd $CWD
echo "entering ./package/${PACKAGE}"
cd packages/${PACKAGE}

echo Build started on `date`
echo Building the Docker image...
docker build --build-arg ENV=${ENV} -t $IMAGE_REPO_NAME:$IMAGE_TAG -t $IMAGE_REPO_NAME:latest .
docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $ECR_REPO_URL:$IMAGE_TAG
docker tag $IMAGE_REPO_NAME:latest $ECR_REPO_URL:latest

echo Build completed on `date`

# Push image to ECR
echo Pushing the Docker images...
docker push $ECR_REPO_URL:$IMAGE_TAG
docker push $ECR_REPO_URL:latest

# # Start ECS service via Terraform
# cd $CWD
# echo "entering ./terraform/${PACKAGE}"
# cd terraform/${PACKAGE}

# echo "Reset Terraform"
# rm -rf .terraform

# echo "Run Terraform init"
# terraform init -backend-config=config/${ENV}/backend.hcl

# echo "Run Terraform apply"
# terraform apply -var-file=./config/${ENV}/terraform.tfvars
